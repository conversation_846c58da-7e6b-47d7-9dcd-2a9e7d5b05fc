const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ownerOf-B_BQ_z-A.js","assets/index-DqIOtWQQ.js","assets/index-CkHrKuVD.css","assets/detectExtension-oOtzSb2z.js"])))=>i.map(i=>d[i]);
import{r as d,f1 as u,_ as o,f2 as a}from"./index-DqIOtWQQ.js";import{f}from"./fetchTokenMetadata-DuBT6m_5.js";const s="0xc87b56dd",I=[{type:"uint256",name:"_tokenId"}],k=[{type:"string"}];async function l(t){return d({contract:t.contract,method:[s,I,k],params:[t.tokenId]})}const y="0x4f6ccce7",h=[{type:"uint256",name:"_index"}],_=[{type:"uint256"}];async function m(t){return d({contract:t.contract,method:[y,h,_],params:[t.index]})}async function w(t){const{useIndexer:c=!0}=t;if(c)try{return await T(t)}catch{return await e(t)}return await e(t)}async function T(t){const c=await u({client:t.contract.client,chain:t.contract.chain,contractAddress:t.contract.address,tokenId:t.tokenId,includeOwners:t.includeOwner});return c||e(t)}async function e(t){let c=t.tokenId;if(t.tokenByIndex)try{c=await m({contract:t.contract,index:t.tokenId})}catch{}const[n,r]=await Promise.all([l({contract:t.contract,tokenId:c}).catch(()=>null),t.includeOwner?o(()=>import("./ownerOf-B_BQ_z-A.js"),__vite__mapDeps([0,1,2,3])).then(i=>i.ownerOf({contract:t.contract,tokenId:c})).catch(()=>null):null]);return n!=null&&n.trim()?a(await f({client:t.contract.client,tokenId:c,tokenUri:n}).catch(()=>({id:c,type:"ERC721",uri:n})),{tokenId:c,tokenUri:n,type:"ERC721",owner:r,tokenAddress:t.contract.address,chainId:t.contract.chain.id}):a({id:c,type:"ERC721",uri:""},{tokenId:c,tokenUri:"",type:"ERC721",owner:r,tokenAddress:t.contract.address,chainId:t.contract.chain.id})}export{w as getNFT};
