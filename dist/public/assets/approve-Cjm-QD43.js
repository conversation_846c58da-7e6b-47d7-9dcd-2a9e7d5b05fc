const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/decimals-gjAL5u0n.js","assets/index-DqIOtWQQ.js","assets/index-CkHrKuVD.css","assets/decimals-BEpkki-P.js"])))=>i.map(i=>d[i]);
import{r as n,p as c,o as i,_ as o,aR as d}from"./index-DqIOtWQQ.js";const u="0xdd62ed3e",y=[{type:"address",name:"owner"},{type:"address",name:"spender"}],m=[{type:"uint256"}];async function _(e){return n({contract:e.contract,method:[u,y,m],params:[e.owner,e.spender]})}const v="0x095ea7b3",l=[{type:"address",name:"spender"},{type:"uint256",name:"value"}],w=[{type:"bool"}];function P(e){const r=i(async()=>"asyncParams"in e?await e.asyncParams():e);return c({contract:e.contract,method:[v,l,w],params:async()=>{const a=await r();return[a.spender,a.value]},value:async()=>{var a;return(a=(await r()).overrides)==null?void 0:a.value},accessList:async()=>{var a;return(a=(await r()).overrides)==null?void 0:a.accessList},gas:async()=>{var a;return(a=(await r()).overrides)==null?void 0:a.gas},gasPrice:async()=>{var a;return(a=(await r()).overrides)==null?void 0:a.gasPrice},maxFeePerGas:async()=>{var a;return(a=(await r()).overrides)==null?void 0:a.maxFeePerGas},maxPriorityFeePerGas:async()=>{var a;return(a=(await r()).overrides)==null?void 0:a.maxPriorityFeePerGas},nonce:async()=>{var a;return(a=(await r()).overrides)==null?void 0:a.nonce},extraGas:async()=>{var a;return(a=(await r()).overrides)==null?void 0:a.extraGas},erc20Value:async()=>{var a;return(a=(await r()).overrides)==null?void 0:a.erc20Value},authorizationList:async()=>{var a;return(a=(await r()).overrides)==null?void 0:a.authorizationList}})}function F(e){return P({contract:e.contract,asyncParams:async()=>{let r;if("amount"in e){const{decimals:a}=await o(async()=>{const{decimals:s}=await import("./decimals-gjAL5u0n.js");return{decimals:s}},__vite__mapDeps([0,1,2,3])),t=await a(e).catch(()=>18);r=d(e.amount.toString(),t)}else r=e.amountWei;return{spender:e.spender,value:r,overrides:{erc20Value:{amountWei:r,tokenAddress:e.contract.address}}}}})}export{_ as a,F as b};
