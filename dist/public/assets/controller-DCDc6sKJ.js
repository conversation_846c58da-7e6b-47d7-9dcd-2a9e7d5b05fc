const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.es-CXKZB9P5.js","assets/index-DqIOtWQQ.js","assets/index-CkHrKuVD.css"])))=>i.map(i=>d[i]);
import{V as j,W as A,N as p,X as R,_ as Q,Y as z,$ as M,a0 as J,a1 as v,a2 as Y,f as x,a3 as S,a4 as K,M as q,O as X,s as $,a5 as B,S as G,a6 as Z,a7 as O,a8 as aa,a9 as k,aa as na}from"./index-DqIOtWQQ.js";import{U as ea,S as ta}from"./rpc-BbZ1eXyh.js";const b="wallet_addEthereumChain",ia=!0,P={requestedChains:"tw.wc.requestedChains",lastUsedChainId:"tw.wc.lastUsedChainId"};async function ha(a,s,t,c,e){var g,y;const n=await F(a,t,e),i=a.walletConnect;let{onDisplayUri:o}=i||{};if(!o&&e){const f=await R(t);o=m=>{const w=f.mobile.native||f.mobile.universal;if(!w){e(m);return}const E=na(w,m).redirect;e(E)}}o&&n.events.addListener("display_uri",o);let d=i==null?void 0:i.optionalChains,r=a.chain;t==="global.safe"&&(d=W.map(p),r&&!d.includes(r)&&(r=void 0));const{rpcMap:h,requiredChain:l,optionalChains:u}=V({client:a.client,chain:r,optionalChains:d});n.session&&await n.connect({...i!=null&&i.pairingTopic?{pairingTopic:i==null?void 0:i.pairingTopic}:{},optionalChains:u,chains:l?[l.id]:void 0,rpcMap:h}),D(u,c);const C=(await n.enable())[0];if(!C)throw new Error("No accounts found on provider.");const T=A(n.chainId),_=a.chain&&a.chain.id===T?a.chain:p(T);if(a){const f={optionalChains:(g=a.walletConnect)==null?void 0:g.optionalChains,chain:a.chain,pairingTopic:(y=a.walletConnect)==null?void 0:y.pairingTopic};c&&K(c,t,f)}return i!=null&&i.onDisplayUri&&n.events.removeListener("display_uri",i.onDisplayUri),H(C,_,n,s,c,a.client)}async function ua(a,s,t,c,e){const n=c?await j(c,t):null,i=await F(n?{chain:n.chain,client:a.client,walletConnect:{pairingTopic:n.pairingTopic,optionalChains:n.optionalChains}}:{client:a.client,walletConnect:{}},t,e,!0),o=i.accounts[0];if(!o)throw new Error("No accounts found on provider.");const d=A(i.chainId),r=a.chain&&a.chain.id===d?a.chain:p(d);return H(o,r,i,s,c,a.client)}async function F(a,s,t,c=!1){var T,_,g,y;const e=await R(s),n=a.walletConnect,{EthereumProvider:i,OPTIONAL_EVENTS:o,OPTIONAL_METHODS:d}=await Q(async()=>{const{EthereumProvider:f,OPTIONAL_EVENTS:I,OPTIONAL_METHODS:m}=await import("./index.es-CXKZB9P5.js");return{EthereumProvider:f,OPTIONAL_EVENTS:I,OPTIONAL_METHODS:m}},__vite__mapDeps([0,1,2]));let r=n==null?void 0:n.optionalChains,h=a.chain;s==="global.safe"&&(r=W.map(p),h&&!r.includes(h)&&(h=void 0));const{rpcMap:l,requiredChain:u,optionalChains:U}=V({client:a.client,chain:h,optionalChains:r}),C=await i.init({showQrModal:(n==null?void 0:n.showQrModal)===void 0?t?!1:ia:n.showQrModal,projectId:(n==null?void 0:n.projectId)||z,optionalMethods:d,optionalEvents:o,optionalChains:U,chains:u?[u.id]:void 0,metadata:{name:((T=n==null?void 0:n.appMetadata)==null?void 0:T.name)||M().name,description:((_=n==null?void 0:n.appMetadata)==null?void 0:_.description)||M().description,url:((g=n==null?void 0:n.appMetadata)==null?void 0:g.url)||M().url,icons:[((y=n==null?void 0:n.appMetadata)==null?void 0:y.logoUrl)||M().logoUrl]},rpcMap:l,qrModalOptions:n==null?void 0:n.qrModalOptions,disableProviderPing:!0});if(C.events.setMaxListeners(Number.POSITIVE_INFINITY),c||C.session&&await C.disconnect(),s!=="walletConnect"){async function f(){var m,w,E,N;const I=((N=(E=(w=(m=C.session)==null?void 0:m.peer)==null?void 0:w.metadata)==null?void 0:E.redirect)==null?void 0:N.native)||e.mobile.native||e.mobile.universal;t&&I&&await t(I)}C.signer.client.on("session_request_sent",f),C.events.addListener("disconnect",()=>{C.signer.client.off("session_request_sent",f)})}return C}function L({provider:a,address:s,client:t}){return{address:q(s),async sendTransaction(e){const n=await a.request({method:"eth_sendTransaction",params:[{gas:e.gas?v(e.gas):void 0,value:e.value?v(e.value):void 0,from:q(s),to:e.to,data:e.data}]});return X({client:t,walletAddress:q(s),walletType:"walletConnect",transactionHash:n,chainId:e.chainId,contractAddress:e.to??void 0,gasPrice:e.gasPrice}),{transactionHash:n}},async signMessage({message:e}){const n=typeof e=="string"?$(e):e.raw instanceof Uint8Array?B(e.raw):e.raw;return a.request({method:"personal_sign",params:[n,this.address]})},async signTypedData(e){const n=G(e),{domain:i,message:o,primaryType:d}=n,r={EIP712Domain:Z({domain:i}),...n.types};O({domain:i,message:o,primaryType:d,types:r});const h=aa({domain:i??{},message:o,primaryType:d,types:r});return await a.request({method:"eth_signTypedData_v4",params:[this.address,h]})}}}function H(a,s,t,c,e,n){const i=L({provider:t,address:a,client:n});async function o(){t.removeListener("accountsChanged",r),t.removeListener("chainChanged",h),t.removeListener("disconnect",d),await t.disconnect()}function d(){D([],e),e==null||e.removeItem(P.lastUsedChainId),o(),c.emit("disconnect",void 0)}function r(l){if(l[0]){const u=L({provider:t,address:q(l[0]),client:n});c.emit("accountChanged",u),c.emit("accountsChanged",l)}else d()}function h(l){const u=p(A(l));c.emit("chainChanged",u),e==null||e.setItem(P.lastUsedChainId,String(l))}return t.on("accountsChanged",r),t.on("chainChanged",h),t.on("disconnect",d),t.on("session_delete",d),[i,s,o,l=>ra(t,l,e)]}function sa(a){var s,t;return((t=(s=a.session)==null?void 0:s.namespaces[k])==null?void 0:t.methods)||[]}function ca(a){var t,c,e;return((e=(c=(t=a.session)==null?void 0:t.namespaces[k])==null?void 0:c.chains)==null?void 0:e.map(n=>Number.parseInt(n.split(":")[1]||"")))??[]}async function ra(a,s,t){var e,n;const c=s.id;try{const i=ca(a),o=sa(a);if(!i.includes(c)&&o.includes(b)){const r=await J(s),h=[...new Set([...((e=s.blockExplorers)==null?void 0:e.map(u=>u.url))||[],...((n=r.explorers)==null?void 0:n.map(u=>u.url))||[]])];await a.request({method:b,params:[{chainId:v(r.chainId),chainName:r.name,nativeCurrency:r.nativeCurrency,rpcUrls:Y(r),blockExplorerUrls:h.length>0?h:void 0}]});const l=await oa(t);l.push(c),D(l,t)}await a.request({method:"wallet_switchEthereumChain",params:[{chainId:v(c)}]})}catch(i){const o=typeof i=="string"?i:i==null?void 0:i.message;throw/user rejected request/i.test(o)?new ea(i):new ta(i)}}function D(a,s){s==null||s.setItem(P.requestedChains,x(a))}async function oa(a){const s=await a.getItem(P.requestedChains);return s?JSON.parse(s):[]}function V(a){const s={};a.chain&&(s[a.chain.id]=S({chain:a.chain,client:a.client}));const t=((a==null?void 0:a.optionalChains)||[]).slice(0,10);for(const c of t)s[c.id]=S({chain:c,client:a.client});return!a.chain&&t.length===0&&(s[1]=p(1).rpc),{rpcMap:s,requiredChain:a.chain?a.chain:void 0,optionalChains:t.length>0?t.map(c=>c.id):[1]}}const W=[1,11155111,42161,43114,8453,1313161554,84532,56,42220,100,10,137,1101,324,534352];export{ua as autoConnectWC,ha as connectWC};
