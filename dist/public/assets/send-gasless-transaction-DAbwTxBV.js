const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/biconomy-BKAyXvKu.js","assets/index-DqIOtWQQ.js","assets/index-CkHrKuVD.css","assets/openzeppelin-CjN45snR.js","assets/engine-BKsSXtDP.js"])))=>i.map(i=>d[i]);
import{_ as p,f4 as d}from"./index-DqIOtWQQ.js";async function s({account:e,transaction:i,serializableTransaction:a,gasless:n}){if(a.value&&a.value>0n)throw new Error("Gasless transactions cannot have a value");let r;if(n.provider==="biconomy"){const{relayBiconomyTransaction:o}=await p(async()=>{const{relayBiconomyTransaction:t}=await import("./biconomy-BKAyXvKu.js");return{relayBiconomyTransaction:t}},__vite__mapDeps([0,1,2]));r=await o({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(n.provider==="openzeppelin"){const{relayOpenZeppelinTransaction:o}=await p(async()=>{const{relayOpenZeppelinTransaction:t}=await import("./openzeppelin-CjN45snR.js");return{relayOpenZeppelinTransaction:t}},__vite__mapDeps([3,1,2]));r=await o({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(n.provider==="engine"){const{relayEngineTransaction:o}=await p(async()=>{const{relayEngineTransaction:t}=await import("./engine-BKsSXtDP.js");return{relayEngineTransaction:t}},__vite__mapDeps([4,1,2]));r=await o({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(!r)throw new Error("Unsupported gasless provider");return d({address:e.address,transactionHash:r.transactionHash,chainId:i.chain.id}),r}export{s as sendGaslessTransaction};
