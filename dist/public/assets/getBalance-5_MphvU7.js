import{r,ab as c}from"./index-DqIOtWQQ.js";import{getCurrencyMetadata as n}from"./getCurrencyMetadata-SkdHoP-c.js";import"./decimals-BEpkki-P.js";const s="0x70a08231",d=[{type:"address",name:"_address"}],o=[{type:"uint256"}];async function i(a){return r({contract:a.contract,method:[s,d,o],params:[a.address]})}async function y(a){const[t,e]=await Promise.all([i(a),n(a)]);return{...e,value:t,displayValue:c(t,e.decimals),tokenAddress:a.contract.address,chainId:a.contract.chain.id}}export{y as getBalance};
