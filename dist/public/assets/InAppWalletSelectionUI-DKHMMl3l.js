import{bY as n,c1 as i,c2 as s,e_ as d,aG as t,e$ as o,aV as u,b_ as m}from"./index-DqIOtWQQ.js";function S(e){const{screen:l}=n(),c=i(),a=s(e.connectLocale.id);return e.size==="wide"||l!==d.main&&e.size==="compact"?t.jsx(o,{wallet:e.wallet,selectWallet:()=>{c({}),e.select()},client:e.client,connectLocale:e.connectLocale,recommendedWallets:e.recommendedWallets,isActive:l===e.wallet,badge:void 0}):a?t.jsx(m,{disabled:e.disabled,locale:a,wallet:e.wallet,done:e.done,select:e.select,goBack:e.goBack,chain:e.chain,client:e.client,size:e.size}):t.jsx(u,{height:"195px"})}export{S as default};
