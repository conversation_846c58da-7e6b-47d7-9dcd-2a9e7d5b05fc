import{ah as fe,f as Oe,au as et,k as xe,av as S,q as le,n as Rt,aw as ee,ax as Ye,F as Q,ay as jt,a0 as bt,az as q,aA as _e,aB as Tn,aC as jn,U as bn,aD as Cn,aE as Lt,aF as Je,aG as t,aH as u,aI as Pt,aJ as E,aK as tt,aL as k,aM as ie,aN as V,aO as W,aP as An,L as ye,aQ as Re,N as L,ab as Y,aR as Sn,aS as J,aT as Ue,aU as $e,aV as ve,aW as w,aX as ne,aY as qt,aZ as z,a_ as Ze,a$ as He,b0 as H,b1 as we,b2 as R,b3 as N,b4 as Le,b5 as nt,w as In,b6 as Mt,M as Pe,b7 as Te,b8 as pn,b9 as st,ba as qe,bb as Nt,bc as ot,bd as Ve,be as vn,bf as Dn,bg as Bn,bh as Ot,bi as We,bj as $,bk as _t,bl as En,bm as Wn,bn as De,bo as zn,bp as Ct,bq as ae,br as Fn,bs as Rn,bt as Ln,bu as Pn,bv as ge,bw as ze,bx as ke,by as qn,bz as Ut,bA as $t,bB as Mn,bC as At,bD as Nn,bE as Ht,bF as On,bG as St,bH as It,bI as pt,bJ as vt,bK as Vt,bL as Dt,bM as _n,bN as Un,bO as $n,bP as Hn,bQ as Vn,bR as Gn,t as pe,bS as Kn,bT as Qn,bU as Yn,Z as Jn,bV as Zn,bW as Xn,bX as es}from"./index-DqIOtWQQ.js";import{decimals as Be}from"./decimals-gjAL5u0n.js";import{getCurrencyMetadata as Gt}from"./getCurrencyMetadata-SkdHoP-c.js";import{a as it,b as Ee}from"./approve-Cjm-QD43.js";import{f as Kt,d as Qt}from"./AbiFunction-CZH1FNcd.js";import{f as Xe,a as X}from"./Value-zBMbywSI.js";import{isInsightEnabled as ts}from"./common-N-UnSKjk.js";import"./decimals-BEpkki-P.js";async function ns(e){const{originChainId:n,originTokenAddress:s,destinationChainId:o,destinationTokenAddress:r,sender:i,receiver:d,client:a,amount:c,purchaseData:l,maxSteps:f}=e,x=xe(a),y=new URL(`${fe("bridge")}/v1/buy/prepare`),h=await x(y.toString(),{method:"POST",headers:{"Content-Type":"application/json"},body:Oe({buyAmountWei:c.toString(),amount:c.toString(),originChainId:n.toString(),originTokenAddress:s,destinationChainId:o.toString(),destinationTokenAddress:r,sender:i,receiver:d,purchaseData:l,maxSteps:f})});if(!h.ok){const g=await h.json();throw new Error(`${g.code} | ${g.message} - ${g.correlationId}`)}const{data:m}=await h.json();return{originAmount:BigInt(m.originAmount),destinationAmount:BigInt(m.destinationAmount),blockNumber:m.blockNumber?BigInt(m.blockNumber):void 0,timestamp:m.timestamp,estimatedExecutionTimeMs:m.estimatedExecutionTimeMs,steps:m.steps.map(g=>({...g,transactions:g.transactions.map(T=>({...T,value:T.value?BigInt(T.value):void 0,client:a,chain:et(T.chainId)}))})),intent:{originChainId:n,originTokenAddress:s,destinationChainId:o,destinationTokenAddress:r,amount:c,sender:i,receiver:d}}}async function ss(e){const{originChainId:n,originTokenAddress:s,destinationChainId:o,destinationTokenAddress:r,amount:i,sender:d,receiver:a,client:c,purchaseData:l,maxSteps:f}=e,x=xe(c),y=new URL(`${fe("bridge")}/v1/sell/prepare`),h=await x(y.toString(),{method:"POST",headers:{"Content-Type":"application/json"},body:Oe({sellAmountWei:i.toString(),amount:i.toString(),originChainId:n.toString(),originTokenAddress:s,destinationChainId:o.toString(),destinationTokenAddress:r,sender:d,receiver:a,purchaseData:l,maxSteps:f})});if(!h.ok){const g=await h.json();throw new Error(`${g.code} | ${g.message} - ${g.correlationId}`)}const{data:m}=await h.json();return{originAmount:BigInt(m.originAmount),destinationAmount:BigInt(m.destinationAmount),blockNumber:m.blockNumber?BigInt(m.blockNumber):void 0,timestamp:m.timestamp,estimatedExecutionTimeMs:m.estimatedExecutionTimeMs,steps:m.steps.map(g=>({...g,transactions:g.transactions.map(T=>({...T,value:T.value?BigInt(T.value):void 0,client:c,chain:et(T.chainId)}))})),expiration:m.expiration,intent:{originChainId:n,originTokenAddress:s,destinationChainId:o,destinationTokenAddress:r,amount:i,sender:d,receiver:a,purchaseData:l}}}async function os(e){const{chainId:n,tokenAddress:s,sender:o,receiver:r,client:i,amount:d,purchaseData:a,feePayer:c}=e,l=xe(i),f=new URL(`${fe("bridge")}/v1/transfer/prepare`),x=await l(f.toString(),{method:"POST",headers:{"Content-Type":"application/json"},body:Oe({transferAmountWei:d.toString(),amount:d.toString(),chainId:n.toString(),tokenAddress:s,sender:o,receiver:r,purchaseData:a,feePayer:c})});if(!x.ok){const h=await x.json();throw new Error(`${h.code} | ${h.message} - ${h.correlationId}`)}const{data:y}=await x.json();return{originAmount:BigInt(y.originAmount),destinationAmount:BigInt(y.destinationAmount),blockNumber:y.blockNumber?BigInt(y.blockNumber):void 0,timestamp:y.timestamp,estimatedExecutionTimeMs:y.estimatedExecutionTimeMs,steps:y.steps.map(h=>({...h,transactions:h.transactions.map(m=>({...m,value:m.value?BigInt(m.value):void 0,client:i,chain:et(m.chainId)}))})),intent:{chainId:n,tokenAddress:s,amount:d,sender:o,receiver:r,feePayer:c}}}async function is(e){const{id:n,client:s}=e,o=xe(s),r=new URL(`${fe("bridge")}/v1/onramp/status`);r.searchParams.set("id",n);const i=await o(r.toString());if(!i.ok){const a=await i.json();throw new Error(`${a.code||i.status} | ${a.message||i.statusText} - ${a.correlationId||"N/A"}`)}const{data:d}=await i.json();return d}async function as(e){const{client:n,onramp:s,chainId:o,tokenAddress:r,receiver:i,amount:d,purchaseData:a,sender:c,onrampTokenAddress:l,onrampChainId:f,currency:x,maxSteps:y,excludeChainIds:h}=e,m=xe(n),g=`${fe("bridge")}/v1/onramp/prepare`,T={onramp:s,chainId:Number(o),tokenAddress:r,receiver:i};d!==void 0&&(T.amount=d.toString()),a!==void 0&&(T.purchaseData=a),c!==void 0&&(T.sender=c),l!==void 0&&(T.onrampTokenAddress=l),f!==void 0&&(T.onrampChainId=Number(f)),x!==void 0&&(T.currency=x),y!==void 0&&(T.maxSteps=y),h!==void 0&&(T.excludeChainIds=Array.isArray(h)?h.join(","):h);const C=await m(g,{method:"POST",headers:{"Content-Type":"application/json"},body:Oe(T)});if(!C.ok){const D=await C.json();throw new Error(`${D.code||C.status} | ${D.message||C.statusText} - ${D.correlationId||"N/A"}`)}const{data:A}=await C.json(),b=A.steps.map(D=>({...D,originAmount:BigInt(D.originAmount),destinationAmount:BigInt(D.destinationAmount),transactions:D.transactions.map(v=>({...v,value:v.value?BigInt(v.value):void 0}))})),j={...A.intent,amount:A.intent.amount?A.intent.amount:void 0};return{...A,destinationAmount:BigInt(A.destinationAmount),steps:b,intent:j}}async function rs(e){const{transactionHash:n,client:s}=e,o="chainId"in e?e.chainId:e.chain.id,r=xe(s),i=new URL(`${fe("bridge")}/v1/status`);i.searchParams.set("transactionHash",n),i.searchParams.set("chainId",o.toString());const d=await r(i.toString());if(!d.ok){const c=await d.json();throw new Error(`${c.code} | ${c.message} - ${c.correlationId}`)}const{data:a}=await d.json();return a.status==="FAILED"?{status:"FAILED",paymentId:a.paymentId,transactions:a.transactions}:a.status==="PENDING"?{status:"PENDING",originAmount:BigInt(a.originAmount),originChainId:a.originChainId,destinationChainId:a.destinationChainId,originTokenAddress:a.originTokenAddress,destinationTokenAddress:a.destinationTokenAddress,transactions:a.transactions,originToken:a.originToken,destinationToken:a.destinationToken,sender:a.sender,receiver:a.receiver,paymentId:a.paymentId,purchaseData:a.purchaseData}:a.status==="NOT_FOUND"?{status:"NOT_FOUND",paymentId:a.paymentId,transactions:[]}:{status:"COMPLETED",originAmount:BigInt(a.originAmount),destinationAmount:BigInt(a.destinationAmount),originChainId:a.originChainId,destinationChainId:a.destinationChainId,originTokenAddress:a.originTokenAddress,destinationTokenAddress:a.destinationTokenAddress,transactions:a.transactions,originToken:a.originToken,destinationToken:a.destinationToken,sender:a.sender,receiver:a.receiver,paymentId:a.paymentId,purchaseData:a.purchaseData}}async function Yt(e){const{client:n,originChainId:s,originTokenAddress:o,destinationChainId:r,destinationTokenAddress:i,maxSteps:d,sortBy:a,limit:c,offset:l}=e,f=xe(n),x=new URL(`${fe("bridge")}/v1/routes`);s&&x.searchParams.set("originChainId",s.toString()),o&&x.searchParams.set("originTokenAddress",o),r&&x.searchParams.set("destinationChainId",r.toString()),i&&x.searchParams.set("destinationTokenAddress",i),d&&x.searchParams.set("maxSteps",d.toString()),c&&x.searchParams.set("limit",c.toString()),l&&x.searchParams.set("offset",l.toString()),a&&x.searchParams.set("sortBy",a);const y=await f(x.toString());if(!y.ok){const m=await y.json();throw new Error(`${m.code} | ${m.message}`)}const{data:h}=await y.json();return h}function cs(e){const{transaction:n,account:s,supportedDestinations:o}=e,[r,i]=S.useState();return S.useEffect(()=>{Promise.all([le(n.value),le(n.erc20Value),le(n.to),Rt(n)]).then(([d,a,c,l])=>{var f;i({value:d==null?void 0:d.toString(),erc20Value:(f=a==null?void 0:a.amountWei)==null?void 0:f.toString(),erc20Currency:a==null?void 0:a.tokenAddress,to:c,data:l})})},[n]),ee({queryKey:["transaction-cost",n.chain.id,s==null?void 0:s.address,r],queryFn:async()=>{var y,h,m;if(!s)throw new Error("No payer account found");const d=await le(n.erc20Value);if(d){const[g,T,C,A]=await Promise.all([Ye({address:s.address,chain:n.chain,client:n.client,tokenAddress:d.tokenAddress}),Gt({contract:Q({address:d.tokenAddress,chain:n.chain,client:n.client})}),jt(n,s==null?void 0:s.address),bt(n.chain)]),b=d.amountWei,j=g;return{token:{address:d.tokenAddress,name:T.name,symbol:T.symbol,icon:(h=(y=o.find(v=>v.chain.id===n.chain.id))==null?void 0:y.tokens.find(v=>v.address.toLowerCase()===d.tokenAddress.toLowerCase()))==null?void 0:h.icon},decimals:T.decimals,chainMetadata:A,walletBalance:j,gasCostWei:C,transactionValueWei:b}}const[a,c,l]=await Promise.all([Ye({address:s.address,chain:n.chain,client:n.client}),bt(n.chain),jt(n,s==null?void 0:s.address)]),f=a,x=await le(n.value)||0n;return{token:{address:q,name:c.nativeCurrency.name,symbol:c.nativeCurrency.symbol,icon:(m=c.icon)==null?void 0:m.url},chainMetadata:c,decimals:18,walletBalance:f,gasCostWei:l,transactionValueWei:x}},enabled:!!n&&!!r,refetchInterval:e.refetchIntervalMs||3e4})}function me(e){var f,x,y,h;const{client:n,address:s}=e,o=_e(),r=Tn({client:n}),i=o.find(m=>{var g,T;return((T=(g=m.getAccount())==null?void 0:g.address)==null?void 0:T.toLowerCase())===s.toLowerCase()}),d=i&&(i.id==="inApp"||jn(i)||bn(i))?(x=(f=r.data)==null?void 0:f.find(m=>!!m.details.email))==null?void 0:x.details.email:void 0,a=Cn(i==null?void 0:i.id),c=Lt({client:n,address:s}),l=s?c.data||Je(s):"";return t.jsx(u,{flex:"row",style:{justifyContent:"space-between"},children:t.jsxs(u,{flex:"row",center:"y",gap:"sm",color:"secondaryText",children:[i?t.jsx(Pt,{id:i.id,size:E[e.iconSize||"md"],client:e.client}):t.jsx(tt,{size:E[e.iconSize||"md"]}),t.jsxs(u,{flex:"column",gap:"4xs",children:[e.label?t.jsx(k,{size:"xs",color:"secondaryText",children:e.label}):null,t.jsx(k,{size:e.textSize||"xs",color:"primaryText",children:l||Je(e.address)}),r.isLoading?t.jsx(ie,{width:"100px",height:V.sm}):d||(y=a==null?void 0:a.data)!=null&&y.name?t.jsx(k,{size:"xs",color:"secondaryText",children:d||((h=a==null?void 0:a.data)==null?void 0:h.name)}):null]})]})})}function oe(e){return W(e.token)?t.jsx(ds,{chain:e.chain,size:e.size,color:e.color,inline:e.inline}):t.jsx(k,{size:e.size,color:e.color||"primaryText",inline:e.inline,children:e.token.symbol})}function ds(e){const n=An(e.chain);return n.isLoading?t.jsx(ie,{width:"70px",height:V[e.size]}):t.jsx(k,{size:e.size,color:e.color||"primaryText",inline:e.inline,children:n.symbol??"ETH"})}async function Jt(e){const{account:n,transactions:s}=e;if(!n)throw new Error("not connected");if(s.length===0)throw new Error("No transactions to send");const o=s[0];if(!o)throw new Error("No transactions to send");if(n.sendBatchTransaction){const r=await Promise.all(s.map(async d=>{const[a,c,l,f]=await Promise.all([Rt(d),le(d.to),le(d.accessList),le(d.value)]);return{data:a,chainId:d.chain.id,to:c,value:f,accessList:l}}));return{...await n.sendBatchTransaction(r),chain:o.chain,client:o.client}}throw new Error("Account doesn't implement sendBatchTransaction")}async function Bt(e){const n=await ye(e);return Re(n)}async function Zt(e){try{const n=await(async()=>{if(e.toAmount){const l=Q({address:e.toTokenAddress,chain:L(e.toChainId),client:e.client}),f=l.address.toLowerCase()===q?18:await Be({contract:l}),x=Xe(e.toAmount,f);return ns({sender:e.fromAddress,receiver:e.toAddress,originChainId:e.fromChainId,originTokenAddress:e.fromTokenAddress,destinationChainId:e.toChainId,destinationTokenAddress:e.toTokenAddress,amount:x,purchaseData:e.purchaseData,client:e.client})}else if(e.fromAmount){const l=Q({address:e.fromTokenAddress,chain:L(e.fromChainId),client:e.client}),f=await Be({contract:l}),x=Xe(e.fromAmount,f);return ss({sender:e.fromAddress,receiver:e.toAddress,originChainId:e.fromChainId,originTokenAddress:e.fromTokenAddress,destinationChainId:e.toChainId,destinationTokenAddress:e.toTokenAddress,amount:x,purchaseData:e.purchaseData,client:e.client})}throw new Error("Invalid quote request, must provide either `fromAmount` or `toAmount`")})(),s=n.steps[0];if(!s)throw new Error("This quote is incompatible with getBuyWithCryptoQuote. Please use Bridge.Buy.prepare instead.");const o=s.transactions.filter(l=>l.action==="approval");if(o.length>1)throw new Error("This quote is incompatible with getBuyWithCryptoQuote. Please use Bridge.Buy.prepare instead.");const r=o[0],i=s.transactions.filter(l=>l.action!=="approval");if(i.length>1)throw new Error("This quote is incompatible with getBuyWithCryptoQuote. Please use Bridge.Buy.prepare instead.");const d=i[0];if(!d)throw new Error("This quote is incompatible with getBuyWithCryptoQuote. Please use Bridge.Buy.prepare instead.");let a;if(r){const l=Kt(["function approve(address spender, uint256 amount)"]),[f,x]=Qt(l,r.data);a={chainId:s.originToken.chainId,tokenAddress:s.originToken.address,spenderAddress:f,amountWei:x.toString()}}return{transactionRequest:{...d,extraGas:50000n},approvalData:a,swapDetails:{fromAddress:n.intent.sender,toAddress:n.intent.receiver,fromToken:{tokenAddress:s.originToken.address,chainId:s.originToken.chainId,decimals:s.originToken.decimals,symbol:s.originToken.symbol,name:s.originToken.name,priceUSDCents:s.originToken.priceUsd*100},toToken:{tokenAddress:s.destinationToken.address,chainId:s.destinationToken.chainId,decimals:s.destinationToken.decimals,symbol:s.destinationToken.symbol,name:s.destinationToken.name,priceUSDCents:s.destinationToken.priceUsd*100},fromAmount:X(n.originAmount,s.originToken.decimals).toString(),fromAmountWei:n.originAmount.toString(),toAmountMinWei:n.destinationAmount.toString(),toAmountMin:X(n.destinationAmount,s.destinationToken.decimals).toString(),toAmountWei:n.destinationAmount.toString(),toAmount:X(n.destinationAmount,s.destinationToken.decimals).toString(),estimated:{fromAmountUSDCents:Number(X(n.originAmount,s.originToken.decimals))*s.originToken.priceUsd*100,toAmountMinUSDCents:Number(X(n.destinationAmount,s.destinationToken.decimals))*s.destinationToken.priceUsd*100,toAmountUSDCents:Number(X(n.destinationAmount,s.destinationToken.decimals))*s.destinationToken.priceUsd*100,slippageBPS:0,feesUSDCents:0,gasCostUSDCents:0,durationSeconds:s.estimatedExecutionTimeMs/1e3},maxSlippageBPS:0},paymentTokens:[{token:{tokenAddress:s.originToken.address,chainId:s.originToken.chainId,decimals:s.originToken.decimals,symbol:s.originToken.symbol,name:s.originToken.name,priceUSDCents:s.originToken.priceUsd*100},amountWei:n.originAmount.toString(),amount:X(n.originAmount,s.originToken.decimals).toString(),amountUSDCents:Number(X(n.originAmount,s.originToken.decimals))*s.originToken.priceUsd*100}],processingFees:[{token:{tokenAddress:s.originToken.address,chainId:s.originToken.chainId,decimals:s.originToken.decimals,symbol:s.originToken.symbol,name:s.originToken.name,priceUSDCents:s.originToken.priceUsd*100},amountUSDCents:0,amountWei:"0",amount:"0"}],client:e.client}}catch(n){throw console.error("Error getting buy with crypto quote",n),n}}function ls(e,n){return ee({...n,queryKey:["buyWithCryptoQuote",e],refetchInterval:2e4,queryFn:()=>{if(!e)throw new Error("Swap params are required");return Zt(e)},enabled:!!e,retry:!1})}async function us(e){var n,s,o,r;try{if(!e.transactionHash)throw new Error("Transaction hash is required");const i=await rs({transactionHash:e.transactionHash,chainId:e.chainId,client:e.client});switch(i.status){case"COMPLETED":{const d=(n=i.transactions)==null?void 0:n.find(c=>c.chainId===e.chainId),a=(s=i.transactions)==null?void 0:s.find(c=>c.chainId!==e.chainId);return Qe({originTransaction:d,destinationTransaction:a,originAmount:i.originAmount,destinationAmount:i.destinationAmount,originTokenAddress:i.originTokenAddress,destinationTokenAddress:i.destinationTokenAddress,originChainId:i.originChainId,destinationChainId:i.destinationChainId,status:i.status,sender:i.sender,receiver:i.receiver,paymentId:i.paymentId,originToken:i.originToken,destinationToken:i.destinationToken,purchaseData:i.purchaseData})}case"PENDING":return Qe({originAmount:i.originAmount,originTokenAddress:i.originTokenAddress,destinationTokenAddress:i.destinationTokenAddress,originChainId:i.originChainId,destinationChainId:i.destinationChainId,status:i.status,sender:i.sender,receiver:i.receiver,paymentId:i.paymentId,originToken:i.originToken,destinationToken:i.destinationToken,purchaseData:i.purchaseData});case"FAILED":{const d=(o=i.transactions)==null?void 0:o.find(c=>c.chainId===e.chainId),a=(r=i.transactions)==null?void 0:r.find(c=>c.chainId!==e.chainId);return Qe({originTransaction:d,destinationTransaction:a,originAmount:BigInt(0),originTokenAddress:"",destinationTokenAddress:"",originChainId:0,destinationChainId:0,status:i.status,sender:"",receiver:"",paymentId:"",originToken:void 0,destinationToken:void 0,purchaseData:i.purchaseData})}default:return{status:"NOT_FOUND"}}}catch(i){throw console.error("Fetch error:",i),new Error(`Fetch failed: ${i}`)}}function Qe(e){const{originTransaction:n,destinationTransaction:s,status:o,purchaseData:r,originAmount:i,destinationAmount:d,originTokenAddress:a,destinationTokenAddress:c,originChainId:l,destinationChainId:f,sender:x,receiver:y,originToken:h,destinationToken:m}=e;return{fromAddress:x,toAddress:y,quote:{createdAt:new Date().toISOString(),estimated:{fromAmountUSDCents:0,toAmountMinUSDCents:0,toAmountUSDCents:0,slippageBPS:0,feesUSDCents:0,gasCostUSDCents:0,durationSeconds:0},fromAmount:h?Y(i,h.decimals).toString():"",fromAmountWei:i.toString(),toAmount:m&&d?Y(d,m.decimals).toString():"",toAmountWei:d?d.toString():"",toAmountMin:m?Y(d??BigInt(0),m.decimals).toString():"",toAmountMinWei:d?d.toString():"",fromToken:{tokenAddress:a,chainId:l,decimals:(h==null?void 0:h.decimals)??18,name:(h==null?void 0:h.name)??"",symbol:(h==null?void 0:h.symbol)??"",priceUSDCents:0},toToken:{tokenAddress:c,chainId:f,decimals:(m==null?void 0:m.decimals)??18,name:(m==null?void 0:m.name)??"",symbol:(m==null?void 0:m.symbol)??"",priceUSDCents:0}},swapType:(n==null?void 0:n.chainId)===(s==null?void 0:s.chainId)?"SAME_CHAIN":"CROSS_CHAIN",status:o,subStatus:o==="COMPLETED"?"SUCCESS":"NONE",purchaseData:r,bridge:"STARPORT",destination:{amount:m?Y(d??BigInt(0),m.decimals).toString():"",amountWei:(d==null?void 0:d.toString())??"",token:{tokenAddress:c,chainId:f,decimals:(m==null?void 0:m.decimals)??18,name:(m==null?void 0:m.name)??"",symbol:(m==null?void 0:m.symbol)??"",priceUSDCents:0},amountUSDCents:0,completedAt:new Date().toISOString(),explorerLink:"",transactionHash:(s==null?void 0:s.transactionHash)??""},source:{amount:h?Y(i,h.decimals).toString():"",amountWei:i.toString(),token:{tokenAddress:a,chainId:l,decimals:(h==null?void 0:h.decimals)??18,name:(h==null?void 0:h.name)??"",symbol:(h==null?void 0:h.symbol)??"",priceUSDCents:0},amountUSDCents:0,completedAt:new Date().toISOString(),explorerLink:"",transactionHash:(n==null?void 0:n.transactionHash)??""}}}function Xt(e){return ee({queryKey:["getBuyWithCryptoStatus",e==null?void 0:e.transactionHash],queryFn:async()=>{if(!e)throw new Error("No params");return us(e)},enabled:!!e,refetchInterval:n=>{var o;const s=(o=n.state.data)==null?void 0:o.status;return s==="COMPLETED"||s==="FAILED"?!1:5e3},refetchIntervalInBackground:!0,retry:!0})}async function hs(e){try{const s=(I=>{switch(I){case"STRIPE":return"stripe";case"TRANSAK":return"transak";default:return"coinbase"}})(e.preferredProvider),o=e.toTokenAddress!==q?await Be({contract:Q({client:e.client,address:e.toTokenAddress,chain:L(e.toChainId)})}):18,r=e.toAmount?Sn(e.toAmount,o):void 0,i=await as({client:e.client,onramp:s,chainId:e.toChainId,tokenAddress:e.toTokenAddress,receiver:e.toAddress,sender:e.fromAddress,amount:r,purchaseData:e.purchaseData,currency:e.fromCurrencySymbol,maxSteps:2,onrampTokenAddress:q}),a=i.steps.length>0?i.steps[0]:void 0,c=Math.max(120,Math.ceil(i.steps.reduce((I,F)=>I+F.estimatedExecutionTimeMs,0)/1e3)),l=i.destinationAmount,f=e.maxSlippageBPS??0,x=l*BigInt(f)/10000n,y=l-x,h=Y(l,o),m=Y(y,o),g=I=>({chainId:I.chainId,tokenAddress:I.address,decimals:I.decimals,priceUSDCents:Math.round(I.priceUsd*100),name:I.name,symbol:I.symbol}),T=i.destinationToken,C=i.steps.length>0&&a?a.originToken:T,A=i.steps.length>1?i.steps[1].originToken:void 0,b=i.steps.length>0&&a?a.originAmount:i.destinationAmount,j=Y(b,C.decimals),D={amount:j,amountWei:b.toString(),amountUSDCents:Math.round(Number(j)*C.priceUsd*100),token:g(C)};let v;if(A){const I=i.steps[1].originAmount,F=Y(I,A.decimals);v={amount:F,amountWei:I.toString(),amountUSDCents:Math.round(Number(F)*A.priceUsd*100),token:g(A)}}return{estimatedDurationSeconds:c,estimatedToAmountMin:h,estimatedToAmountMinWei:l.toString(),toAmountMinWei:y.toString(),toAmountMin:m,fromCurrency:{amount:i.currencyAmount.toString(),amountUnits:Number(i.currencyAmount).toFixed(2),decimals:2,currencySymbol:i.currency},fromCurrencyWithFees:{amount:i.currencyAmount.toString(),amountUnits:Number(i.currencyAmount).toFixed(2),decimals:2,currencySymbol:i.currency},toToken:g(T),toAddress:e.toAddress,fromAddress:e.fromAddress,maxSlippageBPS:f,intentId:i.id,processingFees:[],onRampToken:D,routingToken:v,onRampLink:i.link,provider:e.preferredProvider??"COINBASE"}}catch(n){throw console.error("Error getting buy with fiat quote",n),n}}function ms(e,n){return ee({...n,queryKey:["useBuyWithFiatQuote",e],queryFn:async()=>{if(!e)throw new Error("No params provided");return hs(e)},enabled:!!e,retry:!1})}async function en(e){const n=await is({id:e.intentId,client:e.client});return fs({intentId:e.intentId,result:n})}function fs(e){const{intentId:n,result:s}=e,r={CREATED:"PENDING_PAYMENT",PENDING:"PENDING_PAYMENT",FAILED:"PAYMENT_FAILED",COMPLETED:"ON_RAMP_TRANSFER_COMPLETED"}[s.status];return xs({intentId:n,status:r,purchaseData:s.purchaseData})}function xs(e){const{intentId:n,status:s,purchaseData:o}=e,r={chainId:0,tokenAddress:"",decimals:18,priceUSDCents:0,name:"",symbol:""},i={estimatedOnRampAmount:"0",estimatedOnRampAmountWei:"0",estimatedToTokenAmount:"0",estimatedToTokenAmountWei:"0",fromCurrency:{amount:"0",amountUnits:"USD",decimals:2,currencySymbol:"USD"},fromCurrencyWithFees:{amount:"0",amountUnits:"USD",decimals:2,currencySymbol:"USD"},onRampToken:r,toToken:r,estimatedDurationSeconds:0,createdAt:new Date().toISOString()};return{intentId:n,status:s,toAddress:"",fromAddress:"",quote:i,purchaseData:o}}function ys(e){return ee({queryKey:["useBuyWithFiatStatus",e],queryFn:async()=>{if(!e)throw new Error("No params provided");return en(e)},enabled:!!e,refetchInterval:n=>{const s=n.state.data,o=s==null?void 0:s.status;return o==="PAYMENT_FAILED"||o==="ON_RAMP_TRANSFER_COMPLETED"&&(s==null?void 0:s.quote.toToken.chainId)===(s==null?void 0:s.quote.onRampToken.chainId)&&(s==null?void 0:s.quote.toToken.tokenAddress.toLowerCase())===(s==null?void 0:s.quote.onRampToken.tokenAddress.toLowerCase())?!1:5e3},refetchIntervalInBackground:!0,retry:!0,...e==null?void 0:e.queryOptions})}function gs(e){var g,T,C,A,b,j;const{payUiOptions:n,supportedDestinations:s,client:o,onContinue:r,payerAccount:i}=e,d=J(),a=Ue(),c=n.metadata,l=n.paymentInfo,{data:f}=$e(l.chain),{data:x}=Lt({client:o,address:l.sellerAddress}),h=ee({queryKey:["amount",l],queryFn:async()=>{let D=18;l.token&&!W(l.token)&&(D=await Be({contract:Q({address:l.token.address,chain:l.chain,client:o})}));let v;return"amountWei"in l?v=Y(l.amountWei,D):v=l.amount,v}}).data;if(!f||h===void 0)return t.jsx(ve,{});const m=l.token?{...l.token,icon:((g=l.token)==null?void 0:g.icon)||((C=(T=s.find(D=>D.chain.id===l.chain.id))==null?void 0:T.tokens.find(D=>{var v;return D.address.toLowerCase()===((v=l.token)==null?void 0:v.address.toLowerCase())}))==null?void 0:C.icon)}:{address:q,name:f.nativeCurrency.name,symbol:f.nativeCurrency.symbol,icon:(A=f.icon)==null?void 0:A.url};return t.jsxs(u,{px:"lg",children:[t.jsx(w,{y:"lg"}),t.jsx(ne,{title:(c==null?void 0:c.name)||"Payment Details"}),t.jsx(w,{y:"lg"}),t.jsxs(u,{children:[c!=null&&c.image?t.jsx(qt,{client:o,src:c==null?void 0:c.image,style:{width:"100%",borderRadius:z.md,backgroundColor:d.colors.tertiaryBg}}):a?t.jsxs(u,{flex:"row",center:"both",style:{padding:z.md,marginBottom:z.md,borderRadius:z.md,backgroundColor:d.colors.tertiaryBg},children:[t.jsx(Pt,{size:E.xl,id:a.id,client:o}),t.jsx("div",{style:{flexGrow:1,borderBottom:"6px dotted",borderColor:d.colors.secondaryIconColor,marginLeft:z.md,marginRight:z.md}}),t.jsx(Ze,{client:o,size:E.xl,chainIconUrl:(b=f.icon)==null?void 0:b.url})]}):null,t.jsx(w,{y:"md"}),t.jsxs(u,{flex:"row",children:[t.jsx(u,{flex:"column",expand:!0,children:t.jsx(k,{size:"md",color:"primaryText",weight:700,children:"Price"})}),t.jsx(u,{expand:!0,children:t.jsxs(u,{flex:"row",gap:"xs",center:"y",style:{justifyContent:"right"},children:[t.jsx(He,{chain:l.chain,client:e.client,size:"sm",token:m}),t.jsxs(k,{color:"primaryText",size:"md",weight:700,children:[String(H(Number(h),6))," ",m.symbol]})]})})]}),t.jsx(w,{y:"md"}),t.jsx(we,{}),t.jsx(w,{y:"md"}),t.jsxs(u,{flex:"row",children:[t.jsx(u,{flex:"column",expand:!0,children:t.jsx(k,{size:"xs",color:"secondaryText",children:"Network"})}),t.jsx(u,{expand:!0,children:t.jsxs(u,{flex:"row",gap:"xs",center:"y",style:{justifyContent:"right"},children:[t.jsx(Ze,{chainIconUrl:(j=f.icon)==null?void 0:j.url,size:"xs",client:e.client}),t.jsx(k,{size:"xs",color:"secondaryText",style:{textAlign:"right"},children:f.name})]})})]}),t.jsx(w,{y:"sm"}),t.jsxs(u,{flex:"row",children:[t.jsx(u,{flex:"column",expand:!0,children:t.jsx(k,{size:"xs",color:"secondaryText",children:"Seller"})}),t.jsx(u,{expand:!0,children:t.jsx(u,{flex:"row",gap:"xs",center:"y",style:{justifyContent:"right"},children:t.jsx(k,{size:"xs",color:"secondaryText",style:{textAlign:"right"},children:x||Je(l.sellerAddress)})})})]})]}),t.jsx(w,{y:"xl"}),i?t.jsx(R,{variant:"accent",fullWidth:!0,onClick:()=>{var D;N({event:"choose_payment_method_direct_payment_mode",client:o,walletAddress:i.address,walletType:a==null?void 0:a.id,toChainId:l.chain.id,toToken:(D=l.token)==null?void 0:D.address}),r(h,l.chain,m)},children:"Choose Payment Method"}):t.jsx("div",{children:t.jsx(Le,{...e.connectOptions,client:o,theme:d,connectButton:{style:{width:"100%"}}})}),t.jsx(w,{y:"lg"}),n.showThirdwebBranding!==!1&&t.jsxs(t.Fragment,{children:[t.jsx(nt,{link:"https://playground.thirdweb.com/connect/pay?utm_source=ub_text"}),t.jsx(w,{y:"sm"})]})]})}async function ks({client:e,originChainId:n,originTokenAddress:s}){return In(async()=>{const o=await Yt({client:e,originChainId:n,originTokenAddress:s,maxSteps:1,sortBy:"popularity",limit:1e6}),r=new Set,i=new Set,d=[];for(const a of o){const c=`${a.destinationToken.chainId}:${a.destinationToken.address}`;if(!r.has(c)){r.add(c),i.has(a.destinationToken.chainId)||i.add(a.destinationToken.chainId);const l=d[a.destinationToken.chainId];l||(d[a.destinationToken.chainId]=[]),d[a.destinationToken.chainId]=[...l||[],{address:Mt(a.destinationToken.address),buyWithCryptoEnabled:!0,buyWithFiatEnabled:!0,name:a.destinationToken.name,symbol:a.destinationToken.symbol,icon:a.destinationToken.iconUri}]}}return[...i].map(a=>({chain:L(a),tokens:d[a]||[]}))},{cacheKey:`buy-supported-destinations-${n}:${s}`,cacheTime:5*60*1e3})}function tn(e,n){return ee({queryKey:["destination-tokens",e],queryFn:async()=>ks({client:e})})}function ws(e){return ee({queryKey:["source-tokens",e],queryFn:async()=>{const n=await Yt({client:e.client,destinationChainId:e.destinationChainId,destinationTokenAddress:e.destinationTokenAddress,maxSteps:1,sortBy:"popularity",limit:50}),s=new Set,o=new Set,r=[];for(const i of n){const d=`${i.originToken.chainId}:${i.originToken.address}`;if(!s.has(d)){s.add(d),o.has(i.originToken.chainId)||o.add(i.originToken.chainId);const a=r[i.originToken.chainId];a||(r[i.originToken.chainId]=[]),r[i.originToken.chainId]=[...a||[],{address:Mt(i.originToken.address),buyWithCryptoEnabled:!0,buyWithFiatEnabled:!0,name:i.originToken.name,symbol:i.originToken.symbol,icon:i.originToken.iconUri}]}}return[...o].map(i=>({chain:L(i),tokens:r[i]||[]}))}})}function Ge(e){var r,i,d;const n=tn(e.client),s=e.token,o=W(s)||(d=(i=(r=n.data)==null?void 0:r.find(a=>a.chain.id===e.chain.id))==null?void 0:i.tokens.find(a=>Pe(a.address)===Pe(s.address)))==null?void 0:d.icon;return t.jsx(He,{token:W(s)?{nativeToken:!0}:{address:s.address,icon:s.icon||o},chain:e.chain,client:e.client,size:e.size})}function Ts(e){var D;const{payUiOptions:n,client:s,payerAccount:o,supportedDestinations:r,onContinue:i}=e,{data:d,error:a,isLoading:c,refetch:l}=$e(n.transaction.chain),f=n.metadata,{data:x,error:y,isLoading:h,refetch:m}=cs({transaction:n.transaction,account:o,supportedDestinations:r}),g=J(),T=Ue(),C=Te(),A=pn(T),b=st({address:C==null?void 0:C.address,chain:n.transaction.chain,tokenAddress:W((x==null?void 0:x.token)||qe)||x==null?void 0:x.token.address,client:e.client},{enabled:!!x});if(h||c)return t.jsx(ve,{});if(!C)return t.jsx(u,{style:{minHeight:"350px"},fullHeight:!0,flex:"row",center:"both",children:t.jsxs(u,{animate:"fadein",children:[t.jsx(w,{y:"xxl"}),t.jsx(u,{flex:"row",center:"x",children:t.jsx(tt,{size:E["3xl"]})}),t.jsx(w,{y:"lg"}),t.jsx(k,{center:!0,color:"primaryText",size:"md",children:"Please connect a wallet to continue"}),t.jsx(w,{y:"xl"}),t.jsx(u,{flex:"row",center:"x",style:{width:"100%"},children:t.jsx(Le,{client:s,theme:g,...e.connectOptions})})]})});if(y||a)return t.jsx(u,{style:{minHeight:"350px"},fullHeight:!0,flex:"row",center:"both",children:t.jsx(Nt,{title:(y==null?void 0:y.message)||(a==null?void 0:a.message)||"Something went wrong",onTryAgain:y?m:l})});if(!x||!d)return t.jsx(ve,{});const j=b.data&&b.data.value<x.transactionValueWei;return t.jsxs(u,{px:"lg",children:[t.jsx(w,{y:"lg"}),t.jsx(ne,{title:(f==null?void 0:f.name)||"Transaction"}),t.jsx(w,{y:"lg"}),t.jsxs(u,{children:[f!=null&&f.image?t.jsx(qt,{client:s,src:f==null?void 0:f.image,style:{width:"100%",borderRadius:z.md,border:`1px solid ${g.colors.borderColor}`,backgroundColor:g.colors.tertiaryBg}}):C?t.jsxs(u,{flex:"column",gap:"sm",children:[j&&t.jsxs("div",{children:[t.jsx(k,{color:"danger",size:"xs",center:!0,multiline:!0,children:"Insufficient Funds"}),t.jsx(k,{size:"xs",center:!0,multiline:!0,children:"Select another token or pay with card."})]}),t.jsxs(u,{flex:"row",style:{justifyContent:"space-between",padding:z.sm,marginBottom:z.sm,borderRadius:z.md,backgroundColor:g.colors.tertiaryBg,border:`1px solid ${g.colors.borderColor}`},children:[t.jsx(me,{address:C==null?void 0:C.address,iconSize:"md",client:s}),b.data?t.jsxs(u,{flex:"row",gap:"3xs",center:"y",children:[t.jsx(k,{size:"xs",color:"secondaryText",weight:500,children:ot(b.data,!1)}),t.jsx(oe,{token:x.token,chain:n.transaction.chain,size:"xs",color:"secondaryText"})]}):t.jsx(ie,{width:"70px",height:V.xs})]})]}):null,t.jsx(w,{y:"md"}),t.jsxs(u,{flex:"row",children:[t.jsx(u,{flex:"column",expand:!0,children:t.jsx(k,{size:"md",color:"primaryText",weight:700,children:"Price"})}),t.jsx(u,{expand:!0,children:t.jsxs(u,{flex:"row",gap:"xs",center:"y",style:{justifyContent:"right"},children:[t.jsx(He,{chain:n.transaction.chain,client:e.client,size:"sm",token:x.token}),t.jsxs(k,{color:"primaryText",size:"md",weight:700,children:[String(H(Number(Y(x.transactionValueWei,x.decimals)),6))," ",x.token.symbol]})]})})]}),t.jsx(w,{y:"md"}),t.jsx(we,{}),t.jsx(w,{y:"md"}),t.jsxs(u,{flex:"row",children:[t.jsx(u,{flex:"column",expand:!0,children:t.jsx(k,{size:"xs",color:"secondaryText",children:"Gas Fees"})}),t.jsx(u,{expand:!0,children:t.jsx(u,{flex:"row",gap:"xs",center:"y",style:{justifyContent:"right"},children:t.jsx(k,{color:A?"success":"primaryText",size:"xs",children:A?"Sponsored":`${String(H(Number(Y(x.gasCostWei,d.nativeCurrency.decimals)),6))} ${d.nativeCurrency.symbol}`})})})]}),t.jsx(w,{y:"sm"}),t.jsxs(u,{flex:"row",children:[t.jsx(u,{flex:"column",expand:!0,children:t.jsx(k,{size:"xs",color:"secondaryText",children:"Network"})}),t.jsx(u,{expand:!0,children:t.jsxs(u,{flex:"row",gap:"xs",center:"y",style:{justifyContent:"right"},children:[t.jsx(Ze,{chainIconUrl:(D=d.icon)==null?void 0:D.url,size:"xs",client:e.client}),t.jsx(k,{size:"xs",color:"secondaryText",style:{textAlign:"right"},children:d.name})]})})]})]}),t.jsx(w,{y:"xl"}),o?t.jsx(R,{variant:"accent",fullWidth:!0,onClick:()=>{var B;let v=j?x.transactionValueWei-(((B=b.data)==null?void 0:B.value)||0n):x.transactionValueWei;x.token.address===q&&!A&&(v+=x.gasCostWei),N({event:"choose_payment_method_transaction_mode",client:s,walletAddress:o.address,walletType:T==null?void 0:T.id,toChainId:n.transaction.chain.id,toToken:x.token.address,amountWei:v.toString()}),i(Y(v,x.decimals),n.transaction.chain,x.token)},children:"Choose Payment Method"}):t.jsx("div",{children:t.jsx(Le,{...e.connectOptions,client:s,theme:g,connectButton:{style:{width:"100%"}}})}),t.jsx(w,{y:"lg"}),n.showThirdwebBranding!==!1&&t.jsxs(t.Fragment,{children:[t.jsx(nt,{link:"https://playground.thirdweb.com/connect/pay?utm_source=ub_text"}),t.jsx(w,{y:"sm"})]})]})}const js=e=>t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:e.size,height:e.size,viewBox:"0 0 512 512","aria-hidden":"true",children:[t.jsx("mask",{id:"a",children:t.jsx("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),t.jsxs("g",{mask:"url(#a)",children:[t.jsx("path",{fill:"#d80027",d:"M0 0v512h144l112-64 112 64h144V0H368L256 64 144 0Z"}),t.jsx("path",{fill:"#eee",d:"M144 0h224v512H144Z"}),t.jsx("path",{fill:"#d80027",d:"m301 289 44-22-22-11v-22l-45 22 23-44h-23l-22-34-22 33h-23l23 45-45-22v22l-22 11 45 22-12 23h45v33h22v-33h45z"})]})]}),bs=e=>t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:e.size,height:e.size,viewBox:"0 0 512 512","aria-hidden":"true",children:[t.jsx("mask",{id:"a",children:t.jsx("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),t.jsxs("g",{mask:"url(#a)",children:[t.jsx("path",{fill:"#0052b4",d:"M0 0h512v512H0z"}),t.jsx("path",{fill:"#ffda44",d:"m256 100.2 8.3 25.5H291l-21.7 15.7 8.3 25.6-21.7-15.8-21.7 15.8 8.3-25.6-21.7-15.7h26.8zm-110.2 45.6 24 12.2 18.9-19-4.2 26.5 23.9 12.2-26.5 4.2-4.2 26.5-12.2-24-26.5 4.3 19-19zM100.2 256l25.5-8.3V221l15.7 21.7 25.6-8.3-15.8 21.7 15.8 21.7-25.6-8.3-15.7 21.7v-26.8zm45.6 110.2 12.2-24-19-18.9 26.5 4.2 12.2-23.9 4.2 26.5 26.5 4.2-24 12.2 4.3 26.5-19-19zM256 411.8l-8.3-25.5H221l21.7-15.7-8.3-25.6 21.7 15.8 21.7-15.8-8.3 25.6 21.7 15.7h-26.8zm110.2-45.6-24-12.2-18.9 19 4.2-26.5-23.9-12.2 26.5-4.2 4.2-26.5 12.2 24 26.5-4.3-19 19zM411.8 256l-25.5 8.3V291l-15.7-21.7-25.6 8.3 15.8-21.7-15.8-21.7 25.6 8.3 15.7-21.7v26.8zm-45.6-110.2-12.2 24 19 18.9-26.5-4.2-12.2 23.9-4.2-26.5-26.5-4.2 24-12.2-4.3-26.5 19 19z"})]})]}),Cs=e=>t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",version:"1.1",id:"Layer_1",x:"0px",y:"0px",width:e.size,height:e.size,viewBox:"0 0 512 512","aria-hidden":"true",children:[t.jsx("circle",{fill:"#F0F0F0",cx:"256",cy:"256",r:"256"}),t.jsxs("g",{children:[t.jsx("path",{fill:"#0052B4",d:"M52.92,100.142c-20.109,26.163-35.272,56.318-44.101,89.077h133.178L52.92,100.142z"}),t.jsx("path",{fill:"#0052B4",d:"M503.181,189.219c-8.829-32.758-23.993-62.913-44.101-89.076l-89.075,89.076H503.181z"}),t.jsx("path",{fill:"#0052B4",d:"M8.819,322.784c8.83,32.758,23.993,62.913,44.101,89.075l89.074-89.075L8.819,322.784L8.819,322.784   z"}),t.jsx("path",{fill:"#0052B4",d:"M411.858,52.921c-26.163-20.109-56.317-35.272-89.076-44.102v133.177L411.858,52.921z"}),t.jsx("path",{fill:"#0052B4",d:"M100.142,459.079c26.163,20.109,56.318,35.272,89.076,44.102V370.005L100.142,459.079z"}),t.jsx("path",{fill:"#0052B4",d:"M189.217,8.819c-32.758,8.83-62.913,23.993-89.075,44.101l89.075,89.075V8.819z"}),t.jsx("path",{fill:"#0052B4",d:"M322.783,503.181c32.758-8.83,62.913-23.993,89.075-44.101l-89.075-89.075V503.181z"}),t.jsx("path",{fill:"#0052B4",d:"M370.005,322.784l89.075,89.076c20.108-26.162,35.272-56.318,44.101-89.076H370.005z"})]}),t.jsxs("g",{children:[t.jsx("path",{fill:"#D80027",d:"M509.833,222.609h-220.44h-0.001V2.167C278.461,0.744,267.317,0,256,0   c-11.319,0-22.461,0.744-33.391,2.167v220.44v0.001H2.167C0.744,233.539,0,244.683,0,256c0,11.319,0.744,22.461,2.167,33.391   h220.44h0.001v220.442C233.539,511.256,244.681,512,256,512c11.317,0,22.461-0.743,33.391-2.167v-220.44v-0.001h220.442   C511.256,278.461,512,267.319,512,256C512,244.683,511.256,233.539,509.833,222.609z"}),t.jsx("path",{fill:"#D80027",d:"M322.783,322.784L322.783,322.784L437.019,437.02c5.254-5.252,10.266-10.743,15.048-16.435   l-97.802-97.802h-31.482V322.784z"}),t.jsx("path",{fill:"#D80027",d:"M189.217,322.784h-0.002L74.98,437.019c5.252,5.254,10.743,10.266,16.435,15.048l97.802-97.804   V322.784z"}),t.jsx("path",{fill:"#D80027",d:"M189.217,189.219v-0.002L74.981,74.98c-5.254,5.252-10.266,10.743-15.048,16.435l97.803,97.803   H189.217z"}),t.jsx("path",{fill:"#D80027",d:"M322.783,189.219L322.783,189.219L437.02,74.981c-5.252-5.254-10.743-10.266-16.435-15.047   l-97.802,97.803V189.219z"})]})]}),As=e=>t.jsx("svg",{width:e.size,height:e.size,viewBox:"0 0 32 32",xmlns:"http://www.w3.org/2000/svg",role:"presentation",children:t.jsxs("g",{fill:"none",fillRule:"evenodd",children:[t.jsx("circle",{cx:"16",cy:"16",fill:"#a81b1b",r:"16"}),t.jsx("path",{d:"M17.548 18.711v1.878h5.063v2.288h-5.063V25.5h-3.096v-2.623H9.389v-2.288h5.063v-1.878H9.389v-2.288h4.171L7.5 7.5h3.752l4.8 7.534L20.853 7.5H24.5l-6.086 8.923h4.197v2.288z",fill:"#ffffff"})]})}),Ss=e=>t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",version:"1.1",width:e.size,height:e.size,x:"0px",y:"0px",viewBox:"0 0 512 512","aria-hidden":"true",children:[t.jsx("circle",{fill:"#F0F0F0",cx:"256",cy:"256",r:"256"}),t.jsxs("g",{children:[t.jsx("path",{fill:"#D80027",d:"M244.87,256H512c0-23.106-3.08-45.49-8.819-66.783H244.87V256z"}),t.jsx("path",{fill:"#D80027",d:"M244.87,122.435h229.556c-15.671-25.572-35.708-48.175-59.07-66.783H244.87V122.435z"}),t.jsx("path",{fill:"#D80027",d:"M256,512c60.249,0,115.626-20.824,159.356-55.652H96.644C140.374,491.176,195.751,512,256,512z"}),t.jsx("path",{fill:"#D80027",d:"M37.574,389.565h436.852c12.581-20.529,22.338-42.969,28.755-66.783H8.819   C15.236,346.596,24.993,369.036,37.574,389.565z"})]}),t.jsx("path",{fill:"#0052B4",d:"M118.584,39.978h23.329l-21.7,15.765l8.289,25.509l-21.699-15.765L85.104,81.252l7.16-22.037  C73.158,75.13,56.412,93.776,42.612,114.552h7.475l-13.813,10.035c-2.152,3.59-4.216,7.237-6.194,10.938l6.596,20.301l-12.306-8.941  c-3.059,6.481-5.857,13.108-8.372,19.873l7.267,22.368h26.822l-21.7,15.765l8.289,25.509l-21.699-15.765l-12.998,9.444  C0.678,234.537,0,245.189,0,256h256c0-141.384,0-158.052,0-256C205.428,0,158.285,14.67,118.584,39.978z M128.502,230.4  l-21.699-15.765L85.104,230.4l8.289-25.509l-21.7-15.765h26.822l8.288-25.509l8.288,25.509h26.822l-21.7,15.765L128.502,230.4z   M120.213,130.317l8.289,25.509l-21.699-15.765l-21.699,15.765l8.289-25.509l-21.7-15.765h26.822l8.288-25.509l8.288,25.509h26.822  L120.213,130.317z M220.328,230.4l-21.699-15.765L176.93,230.4l8.289-25.509l-21.7-15.765h26.822l8.288-25.509l8.288,25.509h26.822  l-21.7,15.765L220.328,230.4z M212.039,130.317l8.289,25.509l-21.699-15.765l-21.699,15.765l8.289-25.509l-21.7-15.765h26.822  l8.288-25.509l8.288,25.509h26.822L212.039,130.317z M212.039,55.743l8.289,25.509l-21.699-15.765L176.93,81.252l8.289-25.509  l-21.7-15.765h26.822l8.288-25.509l8.288,25.509h26.822L212.039,55.743z"})]}),nn={shorthand:"USD",countryCode:"US",name:"US Dollar",symbol:"$",icon:Ss},sn=[nn,{shorthand:"CAD",countryCode:"CA",name:"Canadian Dollar",symbol:"$",icon:js},{shorthand:"GBP",countryCode:"GB",name:"British Pound",symbol:"£",icon:Cs},{shorthand:"EUR",countryCode:"EU",name:"Euro",symbol:"€",icon:bs},{shorthand:"JPY",countryCode:"JP",name:"Japanese Yen",symbol:"¥",icon:As},{shorthand:"AUD",countryCode:"AU",name:"Australian Dollar",symbol:"$"},{shorthand:"NZD",countryCode:"NZ",name:"New Zealand Dollar",symbol:"$"}];function on(e,n){return e.icon?t.jsx(e.icon,{size:E[n]}):t.jsx("img",{src:`https://flagsapi.com/${e.countryCode.toUpperCase()}/flat/64.png`,alt:e.shorthand,width:E[n],height:E[n]})}function Is(e){return t.jsxs(u,{children:[t.jsx(u,{p:"lg",children:t.jsx(ne,{title:"Pay with",onBack:e.onBack})}),t.jsx(we,{}),t.jsx(w,{y:"lg"}),t.jsx(u,{flex:"column",gap:"xs",px:"lg",children:sn.map(n=>t.jsxs(ps,{fullWidth:!0,variant:"secondary",onClick:()=>e.onSelect(n),gap:"sm",children:[on(n,"lg"),t.jsxs(u,{flex:"column",gap:"xxs",children:[t.jsx(k,{color:"primaryText",children:n.shorthand}),t.jsx(k,{size:"sm",children:n.name})]})]},n.shorthand))}),t.jsx(w,{y:"lg"})]})}const ps=Ve(R)(()=>{const e=J();return{background:e.colors.tertiaryBg,justifyContent:"flex-start",gap:z.sm,padding:z.sm,"&:hover":{background:e.colors.secondaryButtonBg,transform:"scale(1.01)"},transition:"background 200ms ease, transform 150ms ease"}});function an(e){return typeof e.error=="object"&&e.error.code&&e.error.code==="MINIMUM_PURCHASE_AMOUNT"?{code:"MINIMUM_PURCHASE_AMOUNT",title:"Amount Too Low",message:"The requested amount is less than the minimum purchase. Try another provider or amount."}:(console.error(e),{code:"UNABLE_TO_GET_PRICE_QUOTE",title:"Failed to Find Quote",message:"We couldn't get a quote for this token pair. Select another token or pay with card."})}const rn=S.forwardRef(function(n,s){return t.jsx(vs,{ref:s,children:t.jsx(vn,{children:t.jsxs(u,{p:"lg",children:[t.jsx(Dn,{children:t.jsx(Bn,{type:"button","aria-label":"Close",onClick:n.close,children:t.jsx(Ot,{width:E.md,height:E.md,style:{color:"inherit"}})})}),n.children]})})})}),vs=We(e=>{const n=J();return{zIndex:1e4,borderTopLeftRadius:$.xl,borderTopRightRadius:$.xl,background:n.colors.modalBg,position:"absolute",bottom:0,left:0,right:0,animation:`${Ds} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.1)`,borderTop:`1px solid ${n.colors.borderColor}`}}),Ds=_t`
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`,cn=We(e=>({backgroundColor:J().colors.modalOverlayBg,zIndex:9999,position:"absolute",inset:0,animation:`${En} 400ms cubic-bezier(0.16, 1, 0.3, 1)`}));function dn(){const[e,n]=S.useState(!1),s=S.useRef(null),o=S.useRef(null),r=S.useCallback(()=>new Promise(d=>{var a;if(s.current){const c={easing:"cubic-bezier(0.175, 0.885, 0.32, 1.1)",fill:"forwards",duration:300},l=s.current.animate([{transform:"translateY(100%)",opacity:0}],c);(a=o.current)==null||a.animate([{opacity:0}],c),l.onfinish=()=>d()}else d()}),[]),i=S.useCallback(async d=>{d?n(!0):(await r(),n(!1))},[r]);return S.useLayoutEffect(()=>{if(!e)return;const d=a=>{s.current&&a.target instanceof Node&&!s.current.contains(a.target)&&i(!1)};return requestAnimationFrame(()=>{document.addEventListener("click",d)}),()=>{document.removeEventListener("click",d)}},[e,i]),{drawerRef:s,drawerOverlayRef:o,setIsOpen:i,isOpen:e}}function Bs(e){if(e>3600){const n=Math.floor(e/3600),s=Math.floor(e%3600/60);return`${n} Hours ${s} Minutes`}return e>60?`${Math.ceil(e/60)} Minutes`:`${e}s`}function ln(e){const{estimatedSeconds:n,quoteIsLoading:s}=e;return t.jsxs(u,{bg:"tertiaryBg",flex:"row",borderColor:"borderColor",style:{borderRadius:$.md,borderTopLeftRadius:0,borderTopRightRadius:0,justifyContent:"space-between",alignItems:"center",borderWidth:"1px",borderStyle:"solid"},children:[t.jsxs(u,{flex:"row",center:"y",gap:"xxs",color:"accentText",p:"sm",children:[t.jsx(Wn,{width:E.sm,height:E.sm}),s?t.jsx(ie,{height:V.xs,width:"50px",color:"borderColor"}):t.jsx(k,{size:"xs",color:"secondaryText",children:n!==void 0?`~${Bs(n)}`:"--"})]}),t.jsxs(R,{variant:"ghost",onClick:e.onViewFees,gap:"xs",children:[t.jsx(u,{color:"accentText",flex:"row",center:"both",children:t.jsx(Es,{size:E.sm})}),t.jsx(k,{size:"xs",color:"secondaryText",children:"View Fees"})]})]})}const Es=e=>t.jsxs("svg",{width:e.size,height:e.size,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[t.jsx("path",{d:"M9.5 1.5H2.5C1.94772 1.5 1.5 1.94772 1.5 2.5V9.5C1.5 10.0523 1.94772 10.5 2.5 10.5H9.5C10.0523 10.5 10.5 10.0523 10.5 9.5V2.5C10.5 1.94772 10.0523 1.5 9.5 1.5Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M4.5 7.5L7.5 4.5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"})]});function Ws(e){return t.jsxs(u,{bg:"tertiaryBg",borderColor:"borderColor",flex:"row",style:{borderRadius:$.md,borderBottomRightRadius:0,borderBottomLeftRadius:0,borderWidth:"1px",borderStyle:"solid",borderBottom:"none",flexWrap:"nowrap",justifyContent:"space-between",alignItems:"center"},children:[t.jsxs(zs,{variant:"ghost",onClick:e.onSelectCurrency,style:{minHeight:"64px",justifyContent:"flex-start",minWidth:"50%"},gap:"sm",children:[on(e.currency,"md"),t.jsxs(u,{flex:"row",center:"y",gap:"xxs",color:"secondaryText",children:[t.jsx(k,{color:"primaryText",children:e.currency.shorthand}),t.jsx(De,{width:E.sm,height:E.sm})]})]}),t.jsx("div",{style:{flexGrow:1,flexShrink:1,display:"flex",flexDirection:"column",alignItems:"flex-end",gap:z.xxs,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",justifyContent:"center",paddingRight:z.sm},children:e.isLoading?t.jsx(ie,{width:"100px",height:V.lg}):t.jsx(k,{size:"lg",color:e.value?"primaryText":"secondaryText",children:e.value?`${e.currency.symbol}${H(Number(e.value),6)}`:"--"})})]})}const zs=Ve(R)(()=>({"&[disabled]:hover":{borderColor:"transparent"}}));function Fs(e){return t.jsx(u,{flex:"column",gap:"xs",style:{alignItems:"flex-start"},children:e.quote.processingFees.map(n=>{const s=H(Number(n.amount),6);return t.jsxs(u,{flex:"row",gap:"xxs",children:[t.jsxs(k,{color:"primaryText",size:"sm",children:[s===0?"~":"",s," ",n.token.symbol]}),t.jsxs(k,{color:"secondaryText",size:"sm",children:["($",(n.amountUSDCents/100).toFixed(2),")"]})]},`${n.token.chainId}_${n.token.tokenAddress}_${s}`)})})}function Rs(e){return t.jsxs(u,{flex:"column",gap:"xs",children:[t.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[t.jsx(k,{inline:!0,color:"secondaryText",children:"Amount"}),t.jsxs(k,{color:"primaryText",inline:!0,children:[H(Number(e.quote.fromCurrency.amount),2)," ",e.quote.fromCurrency.currencySymbol]})]}),e.quote.processingFees.map((n,s)=>{const o=H(Number(n.amount),6);return t.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[t.jsx(k,{inline:!0,color:"secondaryText",children:n.feeType==="NETWORK"?"Network Fee":"Processing Fee"}),t.jsxs(k,{color:"primaryText",inline:!0,children:[o===0?"~":""," ",o," ",n.currencySymbol]})]},s)}),t.jsx(w,{y:"xxs"}),t.jsx(we,{}),t.jsx(w,{y:"xxs"}),t.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[t.jsx(k,{inline:!0,color:"secondaryText",children:"Total"}),t.jsxs(k,{color:"primaryText",inline:!0,children:[H(Number(e.quote.fromCurrencyWithFees.amount),6)," ",e.quote.fromCurrencyWithFees.currencySymbol]})]})]})}const Ls=["COINBASE","STRIPE","TRANSAK"];function Ps(e){return t.jsx(u,{expand:!0,flex:"column",gap:"sm",style:{alignItems:"flex-start"},children:Ls.map(n=>t.jsx(u,{flex:"row",expand:!0,style:{justifyContent:"space-between"},children:t.jsx(R,{fullWidth:!0,onClick:()=>e.onSelect(n),variant:"link",children:t.jsx(zn,{color:e.preferredProvider===n?"primaryText":"secondaryText",size:"sm",hoverColor:"primaryText",children:n.charAt(0).toUpperCase()+n.slice(1).toLowerCase()})})},n))})}function qs(e){var p,_,Z,G,M,K,ue,he,re,ce;const{toToken:n,tokenAmount:s,payer:o,client:r,setScreen:i,toChain:d,showCurrencySelector:a,selectedCurrency:c}=e,f=((_=(p=e.payOptions)==null?void 0:p.paymentInfo)==null?void 0:_.sellerAddress)||e.payer.account.address,{drawerRef:x,drawerOverlayRef:y,isOpen:h,setIsOpen:m}=dn(),[g,T]=S.useState("fees"),C=e.payOptions.buyWithFiat,[A,b]=S.useState(C!==!1?(C==null?void 0:C.preferredProvider)||(localStorage.getItem(Ct)??void 0):void 0),j=ms(C!==!1&&s?{fromCurrencySymbol:c.shorthand,toChainId:d.id,toAddress:f,toTokenAddress:W(n)?q:n.address,toAmount:s,client:r,isTestMode:C==null?void 0:C.testMode,purchaseData:e.payOptions.purchaseData,fromAddress:o.account.address,preferredProvider:A}:void 0);function D(){j.data&&i({id:"fiat-flow",quote:j.data})}function v(){j.data&&(T("fees"),m(!0))}function B(){T("providers"),m(!0)}const I=!j.data,F=!j.isLoading&&j.error?an(j.error):void 0;return t.jsxs(u,{flex:"column",gap:"lg",animate:"fadein",children:[h&&t.jsxs(t.Fragment,{children:[t.jsx(cn,{ref:y}),t.jsxs(rn,{ref:x,close:()=>m(!1),children:[g==="fees"&&j.data&&t.jsxs("div",{children:[t.jsx(k,{size:"lg",color:"primaryText",children:"Fees"}),t.jsx(w,{y:"lg"}),t.jsx(Rs,{quote:j.data})]}),g==="providers"&&t.jsxs("div",{children:[t.jsx(k,{size:"lg",color:"primaryText",children:"Providers"}),t.jsx(w,{y:"lg"}),t.jsx(Ps,{preferredProvider:A||((Z=j.data)==null?void 0:Z.provider),onSelect:te=>{b(te),localStorage.setItem(Ct,te),m(!1)}})]})]})]}),t.jsxs(u,{flex:"column",gap:"sm",children:[t.jsx(k,{size:"sm",children:"Pay with card"}),t.jsxs("div",{children:[t.jsx(Ws,{isLoading:j.isLoading,value:(G=j.data)==null?void 0:G.fromCurrencyWithFees.amount,client:r,currency:c,onSelectCurrency:a}),t.jsxs(u,{bg:"tertiaryBg",flex:"row",borderColor:"borderColor",style:{paddingLeft:z.md,justifyContent:"space-between",alignItems:"center",borderWidth:"1px",borderStyle:"solid",borderBottom:"none"},children:[t.jsx(k,{size:"xs",color:"secondaryText",children:"Provider"}),t.jsx(R,{variant:"ghost",onClick:B,children:t.jsxs(u,{flex:"row",center:"y",gap:"xxs",color:"secondaryText",children:[t.jsx(k,{size:"xs",children:A?`${A.charAt(0).toUpperCase()+A.slice(1).toLowerCase()}`:(M=j.data)!=null&&M.provider?`${((K=j.data)==null?void 0:K.provider.charAt(0).toUpperCase())+((ue=j.data)==null?void 0:ue.provider.slice(1).toLowerCase())}`:""}),t.jsx(De,{width:E.sm,height:E.sm})]})})]}),t.jsx(ln,{quoteIsLoading:j.isLoading,estimatedSeconds:(he=j.data)==null?void 0:he.estimatedDurationSeconds,onViewFees:v})]}),F&&t.jsx("div",{children:(re=F.data)!=null&&re.minimumAmountEth?t.jsxs(k,{color:"danger",size:"sm",center:!0,multiline:!0,children:["Minimum amount is"," ",H(Number(F.data.minimumAmountEth),6)," ",t.jsx(oe,{token:n,chain:d,size:"sm",inline:!0,color:"danger"})]}):t.jsxs("div",{children:[t.jsx(k,{color:"danger",size:"xs",center:!0,multiline:!0,children:F.title}),t.jsx(k,{size:"xs",center:!0,multiline:!0,children:F.message})]})})]}),(ce=F==null?void 0:F.data)!=null&&ce.minimumAmountEth?t.jsx(R,{variant:"accent",fullWidth:!0,onClick:()=>{var te;e.setTokenAmount(H(Number((te=F.data)==null?void 0:te.minimumAmountEth),6).toString()),e.setHasEditedAmount(!0)},children:"Set Minimum"}):t.jsx(R,{variant:I?"outline":"accent","data-disabled":I,disabled:I,fullWidth:!0,onClick:()=>{N({event:"confirm_onramp_quote",client:r,walletAddress:o.account.address,walletType:o.wallet.id,toChainId:d.id,toToken:W(n)?void 0:n.address}),D()},gap:"xs",children:j.isLoading?t.jsxs(t.Fragment,{children:["Getting price quote",t.jsx(ae,{size:"sm",color:"accentText"})]}):"Continue"})]})}function Ms(e){const n=e.toToken.chainId===e.onRampToken.token.chainId,s=Pe(e.toToken.tokenAddress)===Pe(e.onRampToken.token.tokenAddress);return!(n&&s)}function Ns(e){return Ms(e)?e.routingToken?[{action:"buy",token:e.onRampToken.token,amount:e.onRampToken.amount},{action:"swap",token:e.routingToken.token,amount:e.routingToken.amount},{action:"bridge",token:e.toToken,amount:e.estimatedToAmountMin}]:[{action:"buy",token:e.onRampToken.token,amount:e.onRampToken.amount},{action:"swap",token:e.toToken,amount:e.estimatedToAmountMin}]:[{action:"buy",token:e.toToken,amount:e.estimatedToAmountMin}]}function Ke(e){const[n,s]=S.useState(!1);return t.jsx(R,{...e,gap:"xs",onClick:async()=>{s(!0);try{await e.switchChain()}catch{}s(!1)},children:n?t.jsxs(t.Fragment,{children:["Switching",t.jsx(ae,{size:"sm",color:"accentButtonText"})]}):"Switch Network"})}function Os(e,n){const r=(window.innerHeight-750)/2,i=(window.innerWidth-500)/2;return window.open(`${e}&theme=${n}`,"thirdweb Pay",`width=500, height=750, top=${r}, left=${i}`)}function un(){const e=J();return t.jsx(u,{flex:"row",center:"both",style:{width:"100%",position:"relative",marginTop:"-10px",marginBottom:"-10px",zIndex:1e3},children:t.jsx(u,{flex:"row",center:"both",style:{borderRadius:"100%",width:"30px",height:"30px",backgroundColor:e.colors.modalBg,border:`1px solid ${e.colors.borderColor}`},children:t.jsx(De,{width:16,height:16})})})}const Et=Fn([]),hn=e=>{const n=Et.getValue();Et.setValue([e,...n])};function mn(e){return t.jsx(u,{flex:"row",center:"both",color:e.isDone?"success":e.isActive?"accentText":"secondaryText",children:t.jsx($s,{children:e.isDone?t.jsx(Rn,{width:E.sm,height:E.sm}):t.jsx(Us,{"data-active":e.isActive})})})}function Me(e){return t.jsxs(u,{flex:"row",center:"y",gap:"xs",style:{fontSize:V.sm},color:e.isDone?"success":e.isActive?"accentText":"secondaryText",children:[t.jsx(mn,{isDone:e.isDone,isActive:e.isActive}),e.label]})}const _s=_t`
0% {
  opacity: 1;
  transform: scale(0.5);
}
100% {
  opacity: 0;
  transform: scale(1.5);
}
`,Us=We(()=>({background:"currentColor",width:"10px",height:"10px",borderRadius:"50%",'&[data-active="true"]':{animation:`${_s} 1s infinite`}})),$s=We(()=>({border:"1px solid currentColor",width:"20px",height:"20px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center"}));function Hs(e){let n="borderColor",s;return e.state==="pending"?(s="Pending",n="accentText"):e.state==="actionRequired"?n="accentText":e.state==="completed"?(s="Completed",n="success"):e.state==="failed"&&(n="danger",s="Failed"),t.jsxs(u,{bg:"tertiaryBg",borderColor:n==="success"?"borderColor":n,py:"sm",px:"md",style:{borderRadius:$.lg,alignItems:"flex-start",borderWidth:"1px",borderStyle:"solid",position:"relative",...e.style},children:[e.children,t.jsxs("div",{style:{position:"absolute",right:z.xs,top:z.xs,display:"flex",gap:z.xs,alignItems:"center"},children:[e.state&&s&&t.jsx(k,{size:"xs",color:n,children:s}),(e.state==="actionRequired"||e.state==="completed")&&t.jsx(mn,{isActive:e.state==="actionRequired",isDone:e.state==="completed"}),e.state==="pending"&&t.jsx(ae,{color:"accentText",size:"sm"}),e.state==="failed"&&t.jsx(u,{color:"danger",flex:"row",center:"both",children:t.jsx(Ln,{width:E.sm,height:E.sm})})]})]})}function Vs(e){var i;const n=_e(),s=Pn({wallet:e.payer.wallet,connectedWallets:n}),o=Ks({quote:e.quote,client:e.client,onSuccess:e.onSuccess,onDone:e.onDone,payer:e.payer,theme:e.theme,isAutoMode:s}),r=(i=o.steps[0])==null?void 0:i.step.token.chainId;return t.jsxs(u,{p:"lg",children:[t.jsx(ne,{title:e.title,onBack:e.onBack}),t.jsx(w,{y:"xl"}),t.jsx(u,{flex:"column",gap:"xs",center:"y",style:{paddingLeft:z.md},children:t.jsx(me,{client:e.client,address:e.receiverAddress,iconSize:"md",textSize:"sm",label:"Recipient wallet"})}),t.jsx(w,{y:"md"}),t.jsx(u,{flex:"column",children:o.steps.map(({step:d,status:a},c)=>t.jsxs(u,{flex:"column",children:[t.jsx(Hs,{state:a,index:c,style:{flex:"1"},children:t.jsx(Gs,{step:d,client:e.client,payer:e.payer,index:c})}),c<o.steps.length-1&&t.jsx(un,{})]},d.action))}),t.jsx(w,{y:"md"}),t.jsx(k,{size:"xs",color:"secondaryText",center:!0,style:{padding:`0 ${z.xl}`},children:"Keep this window open until all transactions are complete."}),t.jsx(w,{y:"lg"}),t.jsx(u,{flex:"column",gap:"md",children:!o.isDone&&r&&r!==e.payer.chain.id?t.jsx(Ke,{fullWidth:!0,variant:"accent",switchChain:async()=>{await e.payer.wallet.switchChain(L(r))}}):t.jsxs(R,{variant:"accent",gap:"sm",fullWidth:!0,onClick:o.handleContinue,disabled:o.isLoading,children:[o.isLoading?"Processing":o.isDone?e.transactionMode?"Continue Transaction":"Done":o.isFailed?"Retry":"Continue",o.isLoading&&t.jsx(ae,{size:"sm",color:"primaryText"})]})})]})}function Gs(e){const{step:n,client:s}=e,o=ge(L(n.token.chainId));return t.jsx(u,{flex:"column",gap:"xs",py:"3xs",children:t.jsxs(u,{flex:"row",center:"y",gap:"sm",style:{display:"flex",justifyContent:"space-between",flexWrap:"nowrap"},children:[t.jsx(Ge,{chain:L(n.token.chainId),client:s,size:"md",token:{address:n.token.tokenAddress}}),t.jsxs(u,{flex:"column",gap:"3xs",center:"y",style:{flex:"1"},children:[t.jsx(k,{size:"sm",color:"primaryText",children:n.action.charAt(0).toUpperCase()+n.action.slice(1)}),t.jsxs(u,{flex:"row",gap:"xs",center:"y",style:{display:"flex",justifyContent:"space-between",flexWrap:"nowrap"},children:[t.jsxs(u,{flex:"row",gap:"xxs",center:"y",style:{flex:"1 1 60%",minWidth:0,maxWidth:"60%",overflow:"hidden",flexWrap:"nowrap"},children:[t.jsx(k,{size:"sm",color:"primaryText",children:H(Number(n.amount),5)}),t.jsx(oe,{token:{address:n.token.tokenAddress,name:n.token.name||"",symbol:n.token.symbol||""},chain:L(n.token.chainId),size:"sm",color:"secondaryText"})]}),t.jsx(u,{flex:"row",gap:"xs",center:"y",style:{flex:"1 1 40%",maxWidth:"40%",minWidth:0,justifyContent:"flex-end",flexWrap:"nowrap"},children:t.jsx(k,{size:"xs",style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:o.name})})]})]})]})})}function Ks(e){const n=Ns(e.quote),[s,o]=S.useState(0),[r,i]=S.useState(),[d,a]=S.useState(null),{uiStatus:c}=Qs({intentId:e.quote.intentId,client:e.client,onSuccess:A=>{n.length===1?e.onSuccess(A):o(b=>b+1)},openedWindow:d}),l=n[s-1],f=n[s],x=Js({client:e.client,payer:e.payer,isFiatFlow:!0}),{uiStatus:y}=Ys({client:e.client,transactionHash:r==null?void 0:r.hash,chainId:r==null?void 0:r.chainId,onSuccess:()=>{s===n.length-1?en({intentId:e.quote.intentId,client:e.client}).then(e.onSuccess):(i(void 0),x.reset(),o(A=>A+1))}}),h=n.map((A,b)=>{let j="unknown";return b===0?j=c:b<s?j="completed":b===s&&(x.isPending?j="pending":x.error?j="failed":r?j=y:j="actionRequired"),{index:b,step:A,status:j}}),m=h.some(A=>A.status==="pending"),g=h.every(A=>A.status==="completed"),T=h.some(A=>A.status==="failed"),C=S.useCallback(async()=>{if(g){e.onDone();return}if(s===0){const A=Os(e.quote.onRampLink,e.theme);N({event:"open_onramp_popup",client:e.client,walletAddress:e.payer.account.address,walletType:e.payer.wallet.id,toChainId:e.quote.onRampToken.token.chainId,toToken:e.quote.onRampToken.token.tokenAddress,amountWei:e.quote.onRampToken.amountWei}),a(A),hn({type:"fiat",intentId:e.quote.intentId})}else if(l&&f&&!r)try{const A=await x.mutateAsync({fromToken:l.token,toToken:f.token,amount:f.amount});i({hash:A.transactionHash,chainId:A.chainId})}catch(A){console.error("Failed to execute swap:",A)}else T&&(i(void 0),x.reset())},[g,s,r,e.quote,e.onDone,x,e.theme,T,x.reset,e.client,e.payer.account.address,e.payer.wallet.id,f,l]);return S.useEffect(()=>{e.isAutoMode&&!m&&!g&&!T&&s>0&&s<n.length&&!r&&C()},[e.isAutoMode,s,r,n.length,C,g,T,m]),{steps:h,handleContinue:C,isLoading:m,isDone:g,isFailed:T}}function Qs(e){var d;const n=ze(),s=ys({intentId:e.intentId,client:e.client,queryOptions:{enabled:!!e.openedWindow}});let o="actionRequired";switch((d=s.data)==null?void 0:d.status){case"ON_RAMP_TRANSFER_COMPLETED":o="completed";break;case"PAYMENT_FAILED":o="failed";break;case"PENDING_PAYMENT":o="pending";break;default:o="actionRequired";break}const r=S.useRef(!1);S.useEffect(()=>{r.current||!e.onSuccess||s.data&&o==="completed"&&(r.current=!0,e.onSuccess(s.data))},[e.onSuccess,s.data,o]),S.useEffect(()=>{if(e.openedWindow&&o==="completed")try{e.openedWindow&&!e.openedWindow.closed&&e.openedWindow.close()}catch(a){console.warn("Failed to close payment window:",a)}},[e.openedWindow,o]);const i=S.useRef(!1);return S.useEffect(()=>{!i.current&&o==="completed"&&(i.current=!0,ke(n))},[o,n]),{uiStatus:o}}function Ys(e){var d;const n=Xt(e.transactionHash&&e.chainId?{client:e.client,transactionHash:e.transactionHash,chainId:e.chainId}:void 0);let s="unknown";switch((d=n.data)==null?void 0:d.status){case"COMPLETED":s="completed";break;case"FAILED":s="failed";break;case"PENDING":case"NOT_FOUND":s="pending";break;case"NONE":s="unknown";break;default:s="unknown";break}const o=S.useRef(!1);S.useEffect(()=>{var a;o.current||!e.onSuccess||((a=n.data)==null?void 0:a.status)==="COMPLETED"&&(o.current=!0,e.onSuccess(n.data))},[e.onSuccess,n]);const r=ze(),i=S.useRef(!1);return S.useEffect(()=>{s==="completed"&&!i.current&&(i.current=!0,ke(r))},[r,s]),{uiStatus:s}}function Js(e){const n=ze();return qn({mutationFn:async s=>{var m;const{fromToken:o,toToken:r,amount:i}=s,d=e.payer.wallet;((m=d.getChain())==null?void 0:m.id)!==o.chainId&&await d.switchChain(L(o.chainId));const a=d.getAccount();if(!a)throw new Error("Payer wallet has no account");const c=await Zt({fromChainId:o.chainId,fromTokenAddress:o.tokenAddress,toAmount:i,toChainId:r.chainId,toTokenAddress:r.tokenAddress,fromAddress:a.address,toAddress:a.address,client:e.client}),l=a.sendBatchTransaction,f=Q({client:e.client,address:c.swapDetails.fromToken.tokenAddress,chain:L(c.swapDetails.fromToken.chainId)}),x=c.approvalData&&await it({contract:f,owner:a.address,spender:c.approvalData.spenderAddress})<BigInt(c.approvalData.amountWei);if(x&&c.approvalData&&!l){N({event:"prompt_swap_approval",client:e.client,walletAddress:a.address,walletType:e.payer.wallet.id,fromToken:c.swapDetails.fromToken.tokenAddress,chainId:c.swapDetails.fromToken.chainId,amountWei:c.swapDetails.fromAmountWei,toToken:c.swapDetails.toToken.tokenAddress,toChainId:c.swapDetails.toToken.chainId});const g=Ee({contract:f,spender:c.approvalData.spenderAddress,amountWei:BigInt(c.approvalData.amountWei)}),T=await ye({account:a,transaction:g});await Re({...T,maxBlocksWaitTime:50}),N({event:"swap_approval_success",client:e.client,walletAddress:a.address,walletType:e.payer.wallet.id,fromToken:c.swapDetails.fromToken.tokenAddress,amountWei:c.swapDetails.fromAmountWei,toToken:c.swapDetails.toToken.tokenAddress,chainId:c.swapDetails.fromToken.chainId,toChainId:c.swapDetails.toToken.chainId})}N({event:"prompt_swap_execution",client:e.client,walletAddress:a.address,walletType:e.payer.wallet.id,fromToken:c.swapDetails.fromToken.tokenAddress,amountWei:c.swapDetails.fromAmountWei,toToken:c.swapDetails.toToken.tokenAddress,chainId:c.swapDetails.fromToken.chainId,toChainId:c.swapDetails.toToken.chainId});const y=c.transactionRequest;let h;if(l&&c.approvalData&&x){const g=Ee({contract:f,spender:c.approvalData.spenderAddress,amountWei:BigInt(c.approvalData.amountWei)});h=await Jt({account:a,transactions:[g,y]})}else h=await ye({account:a,transaction:y});return await Re({...h,maxBlocksWaitTime:50}),N({event:"swap_execution_success",client:e.client,walletAddress:a.address,walletType:e.payer.wallet.id,fromToken:c.swapDetails.fromToken.tokenAddress,amountWei:c.swapDetails.fromAmountWei,toToken:c.swapDetails.toToken.tokenAddress,toChainId:c.swapDetails.toToken.chainId,chainId:c.swapDetails.fromToken.chainId}),{transactionHash:h.transactionHash,chainId:h.chain.id}},onSuccess:()=>{ke(n)}})}function Zs(e){const{payOptions:n,supportedDestinations:s,toChain:o,toToken:r}=e;function i(){const f=s.find(h=>h.chain.id===o.id);if(!f)return{fiat:!1,swap:!1};const x=W(r)?q:r.address,y=f.tokens.find(h=>h.address.toLowerCase()===x.toLowerCase());return y?{fiat:y.buyWithFiatEnabled,swap:y.buyWithCryptoEnabled}:{fiat:!0,swap:!0}}const{fiat:d,swap:a}=i(),c=n.buyWithFiat!==!1&&d,l=n.buyWithCrypto!==!1&&a;return{buyWithFiatEnabled:c,buyWithCryptoEnabled:l}}function Xs(e){var h,m,g,T;const{payOptions:n,supportedDestinations:s}=e,o=n==null?void 0:n.prefillBuy,r=Ut(),i=(o==null?void 0:o.amount)||"",[d,a]=S.useState(i),c=$t(d,300);S.useEffect(()=>{o!=null&&o.amount&&a(o.amount),o!=null&&o.chain&&f(o.chain),o!=null&&o.token&&y(o.token)},[o==null?void 0:o.amount,o==null?void 0:o.chain,o==null?void 0:o.token]);const[l,f]=S.useState((o==null?void 0:o.chain)||n.mode==="transaction"&&((h=n.transaction)==null?void 0:h.chain)||n.mode==="direct_payment"&&((m=n.paymentInfo)==null?void 0:m.chain)||((g=s.find(C=>C.chain.id===(r==null?void 0:r.id)))==null?void 0:g.chain)||((T=s[0])==null?void 0:T.chain)||Mn),[x,y]=S.useState((o==null?void 0:o.token)||n.mode==="direct_payment"&&n.paymentInfo.token||qe);return{toChain:l,setToChain:f,toToken:x,setToToken:y,tokenAmount:d,setTokenAmount:a,deferredTokenAmount:c}}function eo(e){var f,x,y,h,m,g;const{payOptions:n}=e,[s,o]=S.useState(),r=n.buyWithCrypto!==!1&&((x=(f=n.buyWithCrypto)==null?void 0:f.prefillSource)==null?void 0:x.chain)||n.mode==="transaction"&&((y=n.transaction)==null?void 0:y.chain)||n.mode==="direct_payment"&&((h=n.paymentInfo)==null?void 0:h.chain),i=s||r||void 0,[d,a]=S.useState(),c=n.buyWithCrypto!==!1&&((g=(m=n.buyWithCrypto)==null?void 0:m.prefillSource)==null?void 0:g.token)||n.mode==="direct_payment"&&n.paymentInfo.token;return{fromChain:i,setFromChain:o,fromToken:d||c||void 0,setFromToken:a}}function to(e){var d,a;const{payOptions:n}=e,o=(n.buyWithFiat!==!1?(a=(d=n.buyWithFiat)==null?void 0:d.prefillSource)==null?void 0:a.currency:void 0)||no(),[r,i]=S.useState(sn.find(c=>c.shorthand===o)||nn);return{selectedCurrency:r,setSelectedCurrency:i}}function no(){try{const e=Intl.DateTimeFormat().resolvedOptions().timeZone.toLowerCase();return e.includes("london")?"GBP":e.includes("europe")?"EUR":e.includes("japan")?"JPY":e.includes("canada")?"CAD":e.includes("australia")?"AUD":e.includes("new zealand")?"NZD":"USD"}catch{return"USD"}}function Ne(e){const{name:n}=ge(e.chain);return!e.token||!e.chain?t.jsxs(R,{variant:"secondary",fullWidth:!0,style:{fontSize:V.sm,justifyContent:"space-between",paddingTop:z.md,paddingBottom:z.md,...e.style},gap:"xxs",onClick:e.onSelectToken,children:[t.jsx(k,{size:"sm",color:"primaryText",children:"Select payment token"}),t.jsx(u,{color:"primaryText",children:t.jsx(De,{width:E.sm,height:E.sm})})]}):t.jsxs(so,{variant:"secondary",fullWidth:!0,style:{fontSize:V.sm,...e.style},gap:"xxs",onClick:e.onSelectToken,disabled:e.freezeChainAndToken,children:[t.jsxs(u,{flex:"row",center:"y",gap:"sm",children:[t.jsx(Ge,{token:e.token,chain:e.chain,size:"md",client:e.client}),t.jsxs(u,{flex:"column",gap:"4xs",children:[t.jsx(u,{flex:"column",gap:"4xs",children:e.isLoading?t.jsx(ie,{width:"120px",height:V.md,color:"borderColor"}):e.value?t.jsxs(u,{flex:"row",gap:"xxs",center:"y",color:"primaryText",children:[t.jsx(k,{size:"md",color:e.value?"primaryText":"secondaryText",children:H(Number(e.value),6)||""}),t.jsx(oe,{token:e.token,chain:e.chain,color:"secondaryText",size:"sm"})]}):t.jsx(oe,{token:e.token,chain:e.chain,size:"sm"})}),n?t.jsx(k,{size:"xs",color:"secondaryText",children:n}):t.jsx(ie,{width:"90px",height:V.xs})]})]}),!e.freezeChainAndToken&&t.jsx(u,{color:"primaryText",children:t.jsx(De,{width:E.sm,height:E.sm})})]})}const so=Ve(R)(()=>{const e=J();return{background:e.colors.tertiaryBg,border:`1px solid ${e.colors.borderColor}`,justifyContent:"space-between",transition:"background 0.3s",padding:z.sm}});function oo(e){return e.length>10?"26px":e.length>6?"34px":"50px"}function at(e){var o;const n=$t(e.tokenAmount,500),s=ee({queryKey:["cryptoToFiat",e.chain.id,At(e.token),n],queryFn:()=>Nn({client:e.client,chain:e.chain,fromTokenAddress:At(e.token),fromAmount:Number(n),to:"USD"})});return s.isLoading?t.jsx(ie,{width:"50px",height:V.lg}):(o=s.data)!=null&&o.result?t.jsxs(k,{...e,children:["$",H(s.data.result,2).toFixed(2)]}):null}function io(e){const n=()=>{let s=e.value.replace(".","").length;return e.value.includes(".")&&(s+=.3),`calc(${`${Math.max(1,s)}ch`} + 6px)`};return t.jsxs(u,{children:[t.jsx("div",{onClick:s=>{var o;(o=s.currentTarget.querySelector("input"))==null||o.focus()},children:t.jsxs(u,{flex:"row",center:"both",gap:"xs",style:{flexWrap:"nowrap"},children:[t.jsx(Ht,{variant:"outline",pattern:"^[0-9]*[.,]?[0-9]*$",inputMode:"decimal",tabIndex:-1,placeholder:"0",type:"text","data-placeholder":e.value==="",value:e.value||"0",disabled:e.freezeAmount,onClick:s=>{e.value===""&&s.currentTarget.setSelectionRange(s.currentTarget.value.length,s.currentTarget.value.length)},onChange:s=>{let o=s.target.value;o=o.replace(",","."),o.startsWith(".")&&(o=`0${o}`);const r=Number(o);Number.isNaN(r)||(o.startsWith("0")&&!o.startsWith("0.")?e.onChange(o.slice(1)):e.onChange(o))},style:{border:"none",fontSize:oo(e.value),boxShadow:"none",borderRadius:"0",padding:"0",paddingBlock:"2px",fontWeight:600,textAlign:"right",width:n(),maxWidth:"calc(100% - 100px)"}}),t.jsx(oe,{token:e.token,chain:e.chain,size:"lg",color:"secondaryText"})]})}),t.jsx(u,{flex:"row",center:"both",style:{height:V.xl},children:t.jsx(at,{tokenAmount:e.value,token:e.token,chain:e.chain,client:e.client,size:"md"})}),!e.hideTokenSelector&&t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"md"}),t.jsx(u,{flex:"row",center:"x",children:t.jsx(Ne,{token:e.token,chain:e.chain,client:e.client,onSelectToken:e.onSelectToken,freezeChainAndToken:e.freezeChainAndToken})})]})]})}function fn(e){return t.jsx(u,{bg:"tertiaryBg",flex:"row",style:{height:"8px",borderRadius:$.lg},children:t.jsx(u,{bg:"accentText",style:{width:`${e.currentStep/e.steps*95}%`,borderRadius:$.lg},children:null})})}function xn(e){return t.jsxs(u,{gap:"xxs",flex:"column",children:[t.jsxs(u,{flex:"row",gap:"xxs",center:"both",color:"danger",children:[t.jsx(On,{width:E.sm,height:E.sm}),t.jsx(k,{color:"danger",size:"sm",children:e.title})]}),t.jsx(k,{center:!0,size:"xs",children:e.message})]})}function yn(e){const n=J(),s=e.receiver.toLowerCase()!==e.sender.toLowerCase();return t.jsxs(u,{children:[t.jsxs(u,{bg:"tertiaryBg",flex:"column",style:{borderRadius:$.lg,border:`1px solid ${n.colors.borderColor}`},children:[t.jsx(u,{flex:"row",gap:"sm",p:"sm",style:{borderBottom:`1px solid ${n.colors.borderColor}`},children:t.jsx(me,{address:e.sender,client:e.client,iconSize:"md",textSize:"sm"})}),t.jsx(Ne,{token:e.fromToken,chain:e.fromChain,client:e.client,isLoading:!1,value:e.fromAmount,freezeChainAndToken:!0,onSelectToken:()=>{},style:{background:"transparent",borderRadius:0,border:"none"}})]}),t.jsx(un,{}),t.jsxs(u,{flex:"column",bg:"tertiaryBg",style:{borderRadius:$.lg,border:`1px solid ${n.colors.borderColor}`},children:[s&&t.jsx(u,{flex:"row",gap:"sm",p:"sm",style:{borderBottom:`1px solid ${n.colors.borderColor}`},children:t.jsx(me,{address:e.receiver,client:e.client,iconSize:"md",textSize:"sm"})}),t.jsx(Ne,{token:e.toToken,chain:e.toChain,client:e.client,isLoading:!1,value:e.toAmount,freezeChainAndToken:!0,onSelectToken:()=>{},style:{background:"transparent",borderRadius:0,border:"none"}})]})]})}function ao(e){const n=e.quote.approvalData&&e.preApprovedAmount!==void 0&&e.preApprovedAmount<BigInt(e.quote.approvalData.amountWei),s=n&&!e.payer.account.sendBatchTransaction,o=s?"approval":"swap",[r,i]=S.useState(o),[d,a]=S.useState(),[c,l]=S.useState("idle"),f=e.quote.swapDetails.toAddress,x=e.quote.swapDetails.fromAddress,y=S.useMemo(()=>{if(r==="approval"&&c==="error"&&d)return d.toLowerCase().includes("user rejected")||d.toLowerCase().includes("user closed modal")||d.toLowerCase().includes("user denied")?{title:"Failed to Approve",message:"Your wallet rejected the approval request."}:{title:"Failed to Approve",message:"Your wallet failed to approve the transaction for an unknown reason. Please try again or contact support."};if(r==="swap"&&c==="error"&&d)return d.toLowerCase().includes("user rejected")||d.toLowerCase().includes("user closed modal")||d.toLowerCase().includes("user denied")?{title:"Failed to Confirm",message:"Your wallet rejected the confirmation request."}:{title:"Failed to Confirm",message:"Your wallet failed to confirm the transaction for an unknown reason. Please try again or contact support."}},[d,r,c]);return t.jsxs(u,{p:"lg",children:[t.jsx(ne,{title:e.title,onBack:e.onBack}),e.isFiatFlow?t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"lg"}),t.jsx(fn,{steps:2,currentStep:2}),t.jsx(w,{y:"sm"}),t.jsxs(k,{size:"xs",children:["Step 2 of 2 - Converting ",e.fromTokenSymbol," to"," ",e.toTokenSymbol]}),t.jsx(w,{y:"md"})]}):t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"lg"}),t.jsx(k,{size:"sm",children:"Confirm payment"}),t.jsx(w,{y:"md"})]}),t.jsx(yn,{sender:x,receiver:f,client:e.client,fromToken:e.fromToken,fromChain:e.fromChain,toToken:e.toToken,toChain:e.toChain,fromAmount:e.fromAmount,toAmount:e.toAmount}),t.jsx(w,{y:"md"}),s&&t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"sm"}),t.jsxs(u,{gap:"sm",flex:"row",style:{justifyContent:"space-between"},center:"y",color:"accentText",children:[t.jsx(Me,{isDone:r==="swap",isActive:r==="approval",label:r==="approval"?"Approve":"Approved"}),t.jsx(gn,{}),t.jsx(Me,{isDone:!1,label:"Confirm",isActive:r==="swap"})]}),t.jsx(w,{y:"lg"})]}),y&&t.jsxs(t.Fragment,{children:[t.jsx(xn,{title:y.title,message:y.message}),t.jsx(w,{y:"md"})]}),e.payer.chain.id!==e.fromChain.id?t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"xs"}),t.jsx(Ke,{fullWidth:!0,variant:"accent",switchChain:async()=>{await e.payer.wallet.switchChain(e.fromChain)}})]}):t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"xs"}),t.jsxs(R,{variant:"accent",fullWidth:!0,disabled:c==="pending",onClick:async()=>{var g;const h=e.payer.wallet;((g=h.getChain())==null?void 0:g.id)!==e.fromChain.id&&await h.switchChain(e.fromChain);const m=h.getAccount();if(!m)throw new Error("Payer wallet has no account");if(r==="approval"&&e.quote.approvalData)try{l("pending"),N({event:"prompt_swap_approval",client:e.client,walletAddress:m.address,walletType:h.id,fromToken:e.quote.swapDetails.fromToken.tokenAddress,amountWei:e.quote.swapDetails.fromAmountWei,toToken:e.quote.swapDetails.toToken.tokenAddress,toChainId:e.quote.swapDetails.toToken.chainId,chainId:e.quote.swapDetails.fromToken.chainId});const T=Ee({contract:Q({client:e.client,address:e.quote.swapDetails.fromToken.tokenAddress,chain:e.fromChain}),spender:e.quote.approvalData.spenderAddress,amountWei:BigInt(e.quote.approvalData.amountWei)}),C=await ye({account:m,transaction:T});await Re({...C,maxBlocksWaitTime:50}),N({event:"swap_approval_success",client:e.client,walletAddress:m.address,walletType:h.id,fromToken:e.quote.swapDetails.fromToken.tokenAddress,amountWei:e.quote.swapDetails.fromAmountWei,toToken:e.quote.swapDetails.toToken.tokenAddress,toChainId:e.quote.swapDetails.toToken.chainId,chainId:e.quote.swapDetails.fromToken.chainId}),i("swap"),l("idle")}catch(T){console.error(T),a(T.message),l("error")}if(r==="swap"){l("pending");try{N({event:"prompt_swap_execution",client:e.client,walletAddress:m.address,walletType:h.id,fromToken:e.quote.swapDetails.fromToken.tokenAddress,amountWei:e.quote.swapDetails.fromAmountWei,toToken:e.quote.swapDetails.toToken.tokenAddress,toChainId:e.quote.swapDetails.toToken.chainId,chainId:e.quote.swapDetails.fromToken.chainId});const T=e.quote.transactionRequest;let C;if(m.sendBatchTransaction&&e.quote.approvalData&&n){const b=Ee({contract:Q({client:e.client,address:e.quote.swapDetails.fromToken.tokenAddress,chain:e.fromChain}),spender:e.quote.approvalData.spenderAddress,amountWei:BigInt(e.quote.approvalData.amountWei)});C=await Jt({account:m,transactions:[b,T]})}else C=await ye({account:m,transaction:T});N({event:"swap_execution_success",client:e.client,walletAddress:m.address,walletType:h.id,fromToken:e.quote.swapDetails.fromToken.tokenAddress,amountWei:e.quote.swapDetails.fromAmountWei,toToken:e.quote.swapDetails.toToken.tokenAddress,toChainId:e.quote.swapDetails.toToken.chainId,chainId:e.quote.swapDetails.fromToken.chainId}),e.isFiatFlow||hn({type:"swap",txHash:C.transactionHash,chainId:C.chain.id}),e.setSwapTxHash(C.transactionHash)}catch(T){console.error(T),l("error")}}},gap:"xs",children:[r==="approval"&&(c==="pending"?"Approving":"Approve"),r==="swap"&&(c==="pending"?"Confirming":"Confirm"),c==="pending"&&t.jsx(ae,{size:"sm",color:"accentButtonText"})]})]})]})}const gn=We(()=>({height:"4px",background:J().colors.borderColor,flex:1}));function Fe(e){const n=S.useMemo(()=>L(e.chainId),[e.chainId]),{name:s}=ge(n);return t.jsxs(u,{flex:"row",style:{justifyContent:"space-between"},children:[t.jsx(k,{size:"sm",children:e.label}),t.jsxs(u,{flex:"column",gap:"3xs",style:{alignItems:"flex-end"},children:[t.jsxs(u,{flex:"row",gap:"xs",center:"y",children:[t.jsx(Ge,{chain:n,size:"sm",token:{address:e.tokenAddress},client:e.client}),t.jsxs(k,{color:"primaryText",size:"sm",children:[H(Number(e.tokenAmount),6)," ",e.tokenSymbol]})]}),t.jsx(k,{size:"xs",children:s})]})]})}function ro(e){if(e.status==="NOT_FOUND")return{status:"Unknown",color:"secondaryText"};const n=e.subStatus,s=e.status;return n==="WAITING_BRIDGE"?{status:"Bridging",color:"accentText",loading:!0}:n==="PARTIAL_SUCCESS"?{status:"Incomplete",color:"secondaryText"}:s==="PENDING"?{status:"Pending",color:"accentText",loading:!0}:s==="FAILED"?{status:"Failed",color:"danger"}:s==="COMPLETED"?{status:"Completed",color:"success"}:{status:"Unknown",color:"secondaryText"}}function Wt(e){var A,b,j,D,v,B,I,F;let n,s=!0;if(e.type==="status"){const p=e.status;e.hideStatusRow&&(s=!1);const _=p.status==="COMPLETED"&&p.subStatus==="PARTIAL_SUCCESS";n={fromToken:{chainId:p.quote.fromToken.chainId,symbol:p.quote.fromToken.symbol||"",address:p.quote.fromToken.tokenAddress,amount:p.quote.fromAmount},quotedToToken:{chainId:p.quote.toToken.chainId,symbol:p.quote.toToken.symbol||"",address:p.quote.toToken.tokenAddress,amount:p.quote.toAmount},gotToken:p.destination?{chainId:p.destination.token.chainId,symbol:p.destination.token.symbol||"",address:p.destination.token.tokenAddress,amount:p.destination.amount}:void 0,statusMeta:ro(p),estimatedDuration:p.quote.estimated.durationSeconds||0,isPartialSuccess:_,destinationTxHash:(A=p.destination)==null?void 0:A.transactionHash,sourceTxHash:(b=p.source)==null?void 0:b.transactionHash,fromAddress:p.fromAddress,toAddress:p.toAddress}}else{const p=e.quote;n={fromToken:{chainId:p.swapDetails.fromToken.chainId,symbol:p.swapDetails.fromToken.symbol||"",address:p.swapDetails.fromToken.tokenAddress,amount:p.swapDetails.fromAmount},quotedToToken:{chainId:p.swapDetails.toToken.chainId,symbol:p.swapDetails.toToken.symbol||"",address:p.swapDetails.toToken.tokenAddress,amount:p.swapDetails.toAmount},isPartialSuccess:!1,estimatedDuration:p.swapDetails.estimated.durationSeconds||0,fromAddress:p.swapDetails.fromAddress,toAddress:p.swapDetails.toAddress}}const{client:o}=e,{fromToken:r,quotedToToken:i,statusMeta:d,sourceTxHash:a,destinationTxHash:c,isPartialSuccess:l,gotToken:f}=n,x=r.chainId,y=i.chainId,h=ge(L(x)),m=St(L(x)),g=ge(L(y)),T=St(L(y)),C=t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"md"}),t.jsx(we,{}),t.jsx(w,{y:"md"})]});return t.jsxs("div",{children:[t.jsx(Fe,{chainId:r.chainId,client:o,label:"Paid",tokenAmount:r.amount,tokenSymbol:r.symbol||"",tokenAddress:r.address}),C,l&&f?t.jsxs(t.Fragment,{children:[t.jsx(Fe,{chainId:i.chainId,client:o,label:l?"Expected":"Received",tokenAmount:i.amount,tokenSymbol:i.symbol||"",tokenAddress:i.address}),C,t.jsx(Fe,{chainId:f.chainId,client:o,label:"Got",tokenAmount:f.amount,tokenSymbol:f.symbol||"",tokenAddress:f.address})]}):t.jsx(Fe,{chainId:i.chainId,client:o,label:"Received",tokenAmount:i.amount,tokenSymbol:i.symbol||"",tokenAddress:i.address}),t.jsxs(t.Fragment,{children:[C,t.jsxs(u,{flex:"row",style:{justifyContent:"space-between"},children:[t.jsx(k,{size:"sm",children:"Recipient"}),t.jsx(me,{address:n.toAddress,iconSize:"sm",client:o})]})]}),d&&s&&t.jsxs(t.Fragment,{children:[C,t.jsxs(u,{flex:"row",style:{justifyContent:"space-between"},children:[t.jsx(k,{size:"sm",children:"Status"}),t.jsx(u,{flex:"column",gap:"3xs",center:"y",style:{alignItems:"flex-end"},children:t.jsx(k,{color:d.color,size:"sm",children:d.status})})]})]}),t.jsx(w,{y:"lg"}),((D=(j=m.explorers)==null?void 0:j[0])==null?void 0:D.url)&&a&&t.jsxs(It,{fullWidth:!0,variant:"outline",href:pt((v=m.explorers[0])==null?void 0:v.url,a),target:"_blank",gap:"xs",style:{fontSize:V.sm,padding:z.sm},children:["View on ",h.name," Explorer",t.jsx(vt,{width:E.sm,height:E.sm})]}),c&&a!==c&&((I=(B=T==null?void 0:T.explorers)==null?void 0:B[0])==null?void 0:I.url)&&t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"sm"}),t.jsxs(It,{fullWidth:!0,variant:"outline",href:pt((F=T.explorers[0])==null?void 0:F.url,c),target:"_blank",gap:"xs",style:{fontSize:V.sm,padding:z.sm},children:["View on ",g.name," Explorer",t.jsx(vt,{width:E.sm,height:E.sm})]})]})]})}function kn(e){var f,x,y,h,m,g;const{onSuccess:n}=e,[s,o]=S.useState(!1),r=Xt({client:e.client,transactionHash:e.swapTxHash,chainId:e.fromChain.id});let i="pending";((f=r.data)==null?void 0:f.status)==="COMPLETED"?i="success":((x=r.data)==null?void 0:x.status)==="FAILED"&&(i="failed"),((y=r.data)==null?void 0:y.status)==="COMPLETED"&&((h=r.data)==null?void 0:h.subStatus)==="PARTIAL_SUCCESS"&&(i="partialSuccess");const d=S.useRef(!1);S.useEffect(()=>{var T;d.current||!n||((T=r.data)==null?void 0:T.status)==="COMPLETED"&&(d.current=!0,n(r.data))},[n,r]);const a=ze(),c=S.useRef(!1);S.useEffect(()=>{(i==="success"||i==="partialSuccess")&&!c.current&&(c.current=!0,ke(a))},[a,i]);const l=r.data&&r.data.status!=="NOT_FOUND"?t.jsx(Wt,{status:r.data,type:"status",client:e.client}):e.quote?t.jsx(Wt,{type:"quote",quote:e.quote,client:e.client}):null;return s?t.jsx(u,{animate:"fadein",children:t.jsxs(u,{p:"lg",children:[t.jsx(ne,{title:"Transaction Details",onBack:()=>o(!1)}),t.jsx(w,{y:"xl"}),l]})}):t.jsx(u,{animate:"fadein",children:t.jsxs(u,{p:"lg",children:[t.jsx(ne,{title:e.title,onBack:e.onBack}),t.jsx(w,{y:"sm"}),i==="success"&&t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"md"}),t.jsxs(u,{color:"success",flex:"column",center:"x",children:[t.jsx(Vt,{width:E["3xl"],height:E["3xl"]}),t.jsx(w,{y:"sm"}),t.jsx(k,{color:"primaryText",size:"lg",children:"Buy Complete"})]}),t.jsx(w,{y:"xl"}),t.jsx(R,{variant:"outline",fullWidth:!0,onClick:()=>o(!0),children:"View transaction details"}),t.jsx(w,{y:"sm"}),t.jsx(R,{variant:"accent",fullWidth:!0,onClick:e.onDone,children:e.transactionMode?"Continue Transaction":"Done"})]}),i==="partialSuccess"&&((m=r.data)==null?void 0:m.status)!=="NOT_FOUND"&&((g=r.data)==null?void 0:g.destination)&&t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"lg"}),t.jsxs(u,{color:"success",flex:"column",center:"x",children:[t.jsx(Dt,{size:E["3xl"]}),t.jsx(w,{y:"xl"}),t.jsx(k,{color:"primaryText",size:"lg",children:"Incomplete"}),t.jsx(w,{y:"sm"}),t.jsxs(k,{size:"sm",color:"danger",children:["Expected ",r.data.quote.toToken.symbol,", Got"," ",r.data.destination.token.symbol," instead"]})]}),t.jsx(w,{y:"xl"})]}),i==="failed"&&t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"xxl"}),t.jsxs(u,{flex:"column",children:[t.jsxs(u,{flex:"column",center:"both",children:[t.jsx(Dt,{size:E["3xl"]}),t.jsx(w,{y:"xl"}),t.jsx(k,{color:"primaryText",size:"lg",children:"Transaction Failed"}),t.jsx(w,{y:"sm"}),t.jsxs(k,{size:"sm",children:["Your transaction ","couldn't"," be processed"]})]}),t.jsx(w,{y:"xl"}),t.jsx(R,{variant:"outline",fullWidth:!0,onClick:()=>o(!0),children:"View transaction details"}),t.jsx(w,{y:"sm"}),t.jsx(R,{variant:"accent",fullWidth:!0,onClick:e.onTryAgain,children:"Try Again"})]})]}),i==="pending"&&t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"xl"}),t.jsxs(u,{flex:"column",animate:"fadein",center:"both",children:[t.jsx("div",{style:{position:"relative",display:"flex",alignItems:"center",justifyContent:"center"},children:t.jsx(ae,{size:"3xl",color:"accentText"})}),t.jsx(w,{y:"lg"}),t.jsx(k,{color:"primaryText",size:"lg",children:"Buy Pending"}),t.jsx(w,{y:"sm"}),t.jsx(k,{color:"secondaryText",size:"sm",children:"This may take a minute to complete"})]}),t.jsx(w,{y:"xl"})]})]})})}function co(e){const[n,s]=S.useState(),o=e.buyWithCryptoQuote,r=S.useMemo(()=>L(o.swapDetails.fromToken.chainId),[o]),i=S.useMemo(()=>L(o.swapDetails.toToken.chainId),[o]),d=o.swapDetails.fromToken.symbol||"",a=o.swapDetails.toToken.symbol||"",c=o.swapDetails.fromAmount,l=o.swapDetails.toAmount,f=o.swapDetails.toToken,x=o.swapDetails.fromToken,y=S.useMemo(()=>f.tokenAddress===q?qe:{address:f.tokenAddress,name:f.name||"",symbol:f.symbol||""},[f]),h=S.useMemo(()=>x.tokenAddress===q?qe:{address:x.tokenAddress,name:x.name||"",symbol:x.symbol||""},[x]);return n?t.jsx(kn,{title:e.title,onBack:e.onBack,onTryAgain:e.onTryAgain,swapTxHash:n,fromChain:r,client:e.client,onDone:e.onDone,transactionMode:e.transactionMode,isEmbed:e.isEmbed,quote:o,onSuccess:e.onSuccess}):t.jsx(ao,{title:e.title,setSwapTxHash:s,toChain:i,toAmount:l,toTokenSymbol:a,fromChain:r,toToken:y,fromAmount:c,fromToken:h,fromTokenSymbol:d,client:e.client,onBack:e.onBack,onTryAgain:e.onTryAgain,quote:o,isFiatFlow:e.isFiatFlow,payer:e.payer,preApprovedAmount:e.approvalAmount})}function lo(e){var o;const n=J(),s=st({address:e.payerAccount.address,chain:e.chain,tokenAddress:W(e.token)||(o=e.token)==null?void 0:o.address,client:e.client},{enabled:!!e.chain&&!!e.token});return t.jsxs(u,{bg:"tertiaryBg",style:{borderRadius:$.lg,border:`1px solid ${n.colors.borderColor}`,...e.swapRequired?{borderBottom:"none",borderBottomLeftRadius:0,borderBottomRightRadius:0}:{}},children:[t.jsxs(u,{flex:"row",gap:"sm",style:{justifyContent:"space-between",padding:z.sm,borderBottom:`1px solid ${n.colors.borderColor}`},children:[t.jsx(me,{client:e.client,address:e.payerAccount.address}),e.token&&e.chain&&s.data?t.jsxs(u,{flex:"row",gap:"3xs",center:"y",children:[t.jsx(k,{size:"xs",color:"secondaryText",weight:500,children:ot(s.data,!1,4)}),t.jsx(oe,{token:e.token,chain:e.chain,size:"xs",color:"secondaryText"})]}):e.token&&e.chain&&s.isLoading?t.jsx(ie,{width:"70px",height:V.xs}):null]}),t.jsx(Ne,{token:e.token,chain:e.chain,client:e.client,isLoading:e.isLoading,value:e.value,onSelectToken:e.onSelectToken,style:{border:"none",borderRadius:0,borderBottomLeftRadius:!e.token||!e.chain||!e.swapRequired?$.lg:0,borderBottomRightRadius:!e.token||!e.chain||!e.swapRequired?$.lg:0}})]})}function uo(e){var he,re,ce,te,je,be,Ce,Ae,Se,Ie;const{setScreen:n,payer:s,client:o,toChain:r,tokenAmount:i,toToken:d,fromChain:a,fromToken:c,payOptions:l,disableTokenSelection:f}=e,y=((re=(he=e.payOptions)==null?void 0:he.paymentInfo)==null?void 0:re.sellerAddress)||e.activeAccount.address,{drawerRef:h,drawerOverlayRef:m,isOpen:g,setIsOpen:T}=dn(),[C,A]=S.useState("fees"),b=st({address:s.account.address,chain:a,tokenAddress:W(c)||c==null?void 0:c.address,client:o},{enabled:!!a&&!!c}),j=W(c)?q:(ce=c==null?void 0:c.address)==null?void 0:ce.toLowerCase(),D=W(d)?q:d.address.toLowerCase(),v=!!i&&!!a&&!!j&&!((a==null?void 0:a.id)===r.id&&j===D),B=a&&c&&v?{fromAddress:s.account.address,toAddress:y,fromChainId:a.id,fromTokenAddress:W(c)?q:c.address,toChainId:r.id,toTokenAddress:W(d)?q:d.address,toAmount:i,client:o,purchaseData:l.purchaseData}:void 0,I=ls(B,{staleTime:30*1e3,refetchInterval:30*1e3,gcTime:30*1e3}),F=ee({queryKey:["allowance",s.account.address,(te=I.data)==null?void 0:te.approvalData],queryFn:()=>{var de;return(de=I.data)!=null&&de.approvalData?it({contract:Q({client:e.client,address:I.data.swapDetails.fromToken.tokenAddress,chain:L(I.data.swapDetails.fromToken.chainId)}),spender:I.data.approvalData.spenderAddress,owner:e.payer.account.address}):null},enabled:!!((je=I.data)!=null&&je.approvalData),refetchOnMount:!0}),p=v?(be=I.data)==null?void 0:be.swapDetails.fromAmount:i,_=!!p&&!!b.data&&Number(b.data.displayValue)<Number(p),Z=!a||!c||v&&!I.data||_||F.isLoading,G=((Ce=e.payer.wallet.getChain())==null?void 0:Ce.id)!==(a==null?void 0:a.id),M=!I.isLoading&&I.error?an(I.error):void 0;function K(){if((e.payOptions.mode==="direct_payment"||e.payOptions.mode==="fund_wallet")&&!_&&!v)n({id:"transfer-flow"});else if(e.payOptions.mode==="transaction"&&!_&&!v){s.account.address!==y?n({id:"transfer-flow"}):e.onDone();return}I.data&&n({id:"swap-flow",quote:I.data,approvalAmount:F.data??void 0})}function ue(){I.data&&(T(!0),A("fees"))}return t.jsxs(u,{flex:"column",gap:"lg",animate:"fadein",children:[g&&t.jsxs(t.Fragment,{children:[t.jsx(cn,{ref:m}),t.jsx(rn,{ref:h,close:()=>T(!1),children:C==="fees"&&I.data&&t.jsxs("div",{children:[t.jsx(k,{size:"lg",color:"primaryText",children:"Fees"}),t.jsx(w,{y:"lg"}),t.jsx(Fs,{quote:I.data})]})})]}),t.jsxs(u,{flex:"column",gap:"sm",children:[t.jsxs(u,{flex:"row",gap:"xxs",center:"y",children:[t.jsx(k,{size:"sm",children:"Pay with"}),c&&a?t.jsx(oe,{token:c,chain:a,size:"sm",color:"secondaryText"}):"crypto"]}),t.jsxs("div",{children:[t.jsx(lo,{value:p||"",chain:a,token:c,isLoading:I.isLoading&&!p,client:o,freezeChainAndTokenSelection:f,payerAccount:e.payer.account,swapRequired:v,onSelectToken:e.showFromTokenSelector}),v&&a&&c&&t.jsx(ln,{quoteIsLoading:I.isLoading,estimatedSeconds:(Ae=I.data)==null?void 0:Ae.swapDetails.estimated.durationSeconds,onViewFees:ue})]}),M&&t.jsx("div",{children:(Se=M.data)!=null&&Se.minimumAmountEth?t.jsxs(k,{color:"danger",size:"xs",center:!0,multiline:!0,children:["Minimum amount is"," ",H(Number(M.data.minimumAmountEth),6)," ",t.jsx(oe,{token:d,chain:r,size:"sm",inline:!0,color:"danger"})]}):t.jsxs("div",{children:[t.jsx(k,{color:"danger",size:"xs",center:!0,multiline:!0,children:M.title}),t.jsx(k,{size:"xs",center:!0,multiline:!0,children:M.message})]})}),!M&&_&&t.jsxs("div",{children:[t.jsx(k,{color:"danger",size:"xs",center:!0,multiline:!0,children:"Insufficient Funds"}),t.jsx(k,{size:"xs",center:!0,multiline:!0,children:"Select another token or pay with card."})]})]}),(Ie=M==null?void 0:M.data)!=null&&Ie.minimumAmountEth?t.jsx(R,{variant:"accent",fullWidth:!0,onClick:()=>{var de;e.setTokenAmount(H(Number((de=M.data)==null?void 0:de.minimumAmountEth),6).toString()),e.setHasEditedAmount(!0)},children:"Set Minimum"}):_||M?t.jsx(R,{variant:"accent",fullWidth:!0,onClick:()=>e.showFromTokenSelector(),children:"Pay with another token"}):G&&a&&!I.isLoading&&!F.isLoading&&!_&&!I.error?t.jsx(Ke,{variant:"accent",fullWidth:!0,switchChain:async()=>{await e.payer.wallet.switchChain(a)}}):t.jsx(R,{variant:Z?"outline":"accent",fullWidth:!0,"data-disabled":Z,disabled:Z,onClick:async()=>{Z||(K(),N({event:"confirm_swap_quote",client:o,walletAddress:s.account.address,walletType:s.wallet.id,chainId:a.id,fromToken:W(c)?void 0:c.address,toChainId:r.id,toToken:W(d)?void 0:d.address}))},gap:"xs",children:I.isLoading?t.jsxs(t.Fragment,{children:["Getting price quote",t.jsx(ae,{size:"sm",color:"accentText"})]}):"Continue"})]})}const zt=5;function ho(e){const n=[];for(let s=0;s<e.length;s+=zt)n.push(e.slice(s,s+zt));return n}function wn(e){const n=Te(),s=_e(),o=$e(e.toChain);return ee({queryKey:["wallets-and-balances",e.sourceSupportedTokens,e.toChain.id,e.toToken,e.mode,n==null?void 0:n.address,s.map(r=>{var i;return(i=r.getAccount())==null?void 0:i.address})],enabled:!!e.sourceSupportedTokens&&!!o.data&&!!n,queryFn:async()=>{const r=await Promise.all(s.map(async d=>{var c;const a=await mo({wallet:d,accountAddress:n==null?void 0:n.address,sourceSupportedTokens:e.sourceSupportedTokens||[],toChain:e.toChain,toToken:e.toToken,mode:e.mode,client:e.client});return[{id:d.id,address:((c=d.getAccount())==null?void 0:c.address)||""},a]})),i=new Map;for(const d of r)i.set(d[0],d[1]);return i}})}async function mo({wallet:e,accountAddress:n,sourceSupportedTokens:s,toChain:o,toToken:r,mode:i,client:d}){var T,C,A;const a=e.getAccount();if(!a)return[];const c=[],l=Object.keys(s).map(b=>L(Number(b))),x=(await Promise.all(l.map(async b=>({chain:b,enabled:await ts(b)})))).filter(b=>b.enabled).map(b=>b.chain),y=ho(x);await Promise.all(y.map(async b=>{var D;const j=await _n({ownerAddress:a.address,chains:b,client:d});for(const v of j){const B=(D=s[v.chainId])==null?void 0:D.find(I=>I.address.toLowerCase()===v.tokenAddress.toLowerCase());B&&c.push({balance:v,chain:L(v.chainId),token:B})}}));const h=W(r)?{address:q,name:((T=o.nativeCurrency)==null?void 0:T.name)||"",symbol:((C=o.nativeCurrency)==null?void 0:C.symbol)||"",icon:(A=o.icon)==null?void 0:A.url}:r,m={...s,[o.id]:[h,...s[o.id]||[]]},g=[];for(const[b,j]of Object.entries(m)){const D=Number(b),v=L(D);for(const B of j){const I=W(B);c.some(p=>p.chain.id===D&&p.token.address.toLowerCase()===B.address.toLowerCase())&&!I||g.push((async()=>{try{const p=await Ye({address:a.address,chain:v,tokenAddress:I?void 0:B.address,client:d});(B.address.toLowerCase()===h.address.toLowerCase()&&v.id===o.id?!(i==="fund_wallet"&&a.address===n)&&p.value>0n:p.value>0n)&&c.push({balance:p,chain:v,token:B})}catch(p){console.warn(`Failed to fetch balance for ${B.symbol} on chain ${D}`,p)}})())}}await Promise.all(g);{const b={};for(const j of c){const D=`${j.chain.id}-${j.token.address.toLowerCase()}`;b[D]||(b[D]=j)}c.splice(0,c.length,...Object.values(b))}return c.sort((b,j)=>{const D=h.address;return b.chain.id===o.id&&b.token.address===D?-1:j.chain.id===o.id&&j.token.address===D?1:b.chain.id===o.id?-1:j.chain.id===o.id?1:b.chain.id-j.chain.id}),c}function fo(e){var c;const n=_e(),s=Te(),o=Ue(),r=$e(e.toChain),i=J(),d=wn({client:e.client,sourceSupportedTokens:e.sourceSupportedTokens||[],toChain:e.toChain,toToken:e.toToken,mode:e.mode});if(d.isLoading||r.isLoading||!r.data||!e.sourceSupportedTokens)return t.jsx(ve,{});const a=Array.from(((c=d.data)==null?void 0:c.entries())||[]).filter(([l])=>{var f;return!((f=e.hiddenWallets)!=null&&f.includes(l.id))}).filter(([,l])=>l.some(x=>x.balance.value>0));return t.jsxs(u,{animate:"fadein",style:{minHeight:"200px"},children:[a.length===0?t.jsx(u,{flex:"column",gap:"xs",py:"lg",children:t.jsxs(k,{size:"xs",color:"secondaryText",center:!0,children:["No suitable payment token found",t.jsx("br",{}),"in connected wallets"]})}):t.jsxs(u,{flex:"column",gap:"xs",children:[t.jsx(k,{size:"sm",children:"Select payment token"}),t.jsx(w,{y:"xs"})]}),t.jsx(u,{scrollY:!0,style:{maxHeight:"350px"},children:t.jsxs(u,{flex:"column",gap:"sm",children:[a.map(([l,f])=>{const x=l.address,y=n.find(h=>{var m;return((m=h.getAccount())==null?void 0:m.address)===x});return y?t.jsx(xo,{wallet:y,balances:f,client:e.client,address:x,onClick:(h,m,g)=>{N({event:"choose_payment_method_token",client:e.client,walletAddress:s==null?void 0:s.address,walletType:o==null?void 0:o.id,chainId:g.id,fromToken:W(m)?void 0:m.address,toToken:W(e.toToken)?void 0:e.toToken.address,toChainId:e.toChain.id}),e.onSelectToken(h,m,g)}},l.id):null}),a.length>0&&t.jsx(Un,{text:"OR"}),t.jsx(R,{variant:"secondary",fullWidth:!0,onClick:()=>{N({event:"choose_payment_method_another_wallet",client:e.client,walletAddress:s==null?void 0:s.address,walletType:o==null?void 0:o.id,toChainId:e.toChain.id,toToken:W(e.toToken)?void 0:e.toToken.address}),e.onConnect()},bg:"tertiaryBg",style:{border:`1px solid ${i.colors.borderColor}`,padding:z.sm},children:t.jsxs(u,{flex:"row",gap:"sm",center:"y",expand:!0,color:"secondaryIconColor",children:[t.jsx(tt,{size:E.md}),t.jsx(k,{size:"sm",color:"primaryText",children:"Pay with another wallet"})]})}),e.fiatSupported&&t.jsx(R,{variant:"secondary",fullWidth:!0,onClick:()=>{N({event:"choose_payment_method_with_card",client:e.client,walletAddress:s==null?void 0:s.address,walletType:o==null?void 0:o.id,toChainId:e.toChain.id,toToken:W(e.toToken)?void 0:e.toToken.address}),e.onPayWithFiat()},bg:"tertiaryBg",style:{border:`1px solid ${i.colors.borderColor}`,padding:z.sm},children:t.jsxs(u,{flex:"row",gap:"sm",center:"y",expand:!0,color:"secondaryIconColor",children:[t.jsx($n,{width:E.md,height:E.md}),t.jsx(k,{size:"sm",color:"primaryText",children:"Pay with card"})]})})]})})]})}function xo(e){const n=J(),s=e.balances,o=Te(),{disconnect:r}=Hn(),i=(o==null?void 0:o.address)===e.address;return t.jsxs(u,{flex:"column",style:{borderRadius:$.lg,border:`1px solid ${n.colors.borderColor}`},children:[t.jsxs(u,{flex:"row",gap:"sm",bg:"tertiaryBg",style:{justifyContent:"space-between",borderTopRightRadius:$.lg,borderTopLeftRadius:$.lg,padding:z.sm,paddingRight:z.xs,borderBottom:`1px solid ${n.colors.borderColor}`},children:[t.jsx(me,{...e}),!i&&t.jsx(R,{variant:"ghost",onClick:()=>r(e.wallet),style:{padding:z.xxs,color:n.colors.secondaryText},children:t.jsx(Ot,{width:E.sm,height:E.sm})})]}),t.jsx(u,{flex:"column",children:e.balances.length>0?s.map((d,a)=>t.jsx(yo,{client:e.client,onClick:()=>e.onClick(e.wallet,d.token,d.chain),tokenBalance:d,wallet:e.wallet,style:{borderTopLeftRadius:0,borderTopRightRadius:0,borderBottomRightRadius:a===s.length-1?$.lg:0,borderBottomLeftRadius:a===s.length-1?$.lg:0,borderBottom:a===s.length-1?"none":`1px solid ${n.colors.borderColor}`}},`${d.token.address}-${d.chain.id}`)):t.jsx(u,{style:{padding:z.sm},children:t.jsx(k,{size:"sm",color:"secondaryText",children:"Insufficient Funds"})})})]})}function yo(e){const{tokenBalance:n,wallet:s,onClick:o,client:r,style:i}=e,d=ge(n.chain);return t.jsxs(go,{onClick:()=>o(n.token,s),variant:"secondary",style:{...i,display:"flex",justifyContent:"space-between",minWidth:0},children:[t.jsxs(u,{flex:"row",center:"y",gap:"sm",style:{flex:"1 1 50%",minWidth:0,maxWidth:"50%",overflow:"hidden",flexWrap:"nowrap"},children:[t.jsx(He,{token:n.token,chain:n.chain,size:"md",client:r}),t.jsxs(u,{flex:"column",gap:"4xs",style:{minWidth:0},children:[t.jsx(k,{size:"xs",color:"primaryText",style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:n.token.symbol}),d&&t.jsx(k,{size:"xs",style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:d.name})]})]}),t.jsxs(u,{flex:"row",center:"y",gap:"4xs",color:"secondaryText",style:{flex:"1 1 50%",maxWidth:"50%",minWidth:0,justifyContent:"flex-end",flexWrap:"nowrap"},children:[t.jsxs(u,{flex:"column",color:"secondaryText",gap:"4xs",style:{alignItems:"flex-end",minWidth:0,overflow:"hidden"},children:[t.jsx(k,{size:"xs",color:"primaryText",style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:ot(n.balance,!0,2)}),t.jsx(at,{tokenAmount:n.balance.displayValue,token:n.token,chain:n.chain,client:r,size:"xs"})]}),t.jsx(Vn,{width:E.md,height:E.md,style:{flexShrink:0}})]})]})}const go=Ve(R)(e=>{const n=J();return{background:"transparent",justifyContent:"space-between",flexWrap:"nowrap",flexDirection:"row",padding:z.sm,paddingRight:z.xs,gap:z.sm,"&:hover":{background:n.colors.secondaryButtonBg,transform:"scale(1.01)"},transition:"background 200ms ease, transform 150ms ease",...e.style}});async function ko(e){try{const n=Q({address:e.tokenAddress,chain:L(e.chainId),client:e.client}),s=n.address.toLowerCase()===q?18:await Be({contract:n}),o=Xe(e.amount,s),r=await os({chainId:e.chainId,tokenAddress:e.tokenAddress,amount:o,sender:e.fromAddress,receiver:e.toAddress,client:e.client,feePayer:e.feePayer}),i=r.steps[0];if(!i)throw new Error("This quote is incompatible with getBuyWithCryptoTransfer. Please use Bridge.Transfer.prepare instead.");const d=i.transactions.filter(y=>y.action==="approval");if(d.length>1)throw new Error("This quote is incompatible with getBuyWithCryptoTransfer. Please use Bridge.Transfer.prepare instead.");const a=d[0];let c;if(a){const y=Kt(["function approve(address spender, uint256 amount)"]),[h,m]=Qt(y,a.data);c={chainId:i.originToken.chainId,tokenAddress:i.originToken.address,spenderAddress:h,amountWei:m.toString()}}const l=i.transactions.filter(y=>y.action!=="approval");if(l.length>1)throw new Error("This quote is incompatible with getBuyWithCryptoTransfer. Please use Bridge.Transfer.prepare instead.");const f=l[0];if(!f)throw new Error("This quote is incompatible with getBuyWithCryptoTransfer. Please use Bridge.Transfer.prepare instead.");return{transactionRequest:{...f,extraGas:50000n},approvalData:c,fromAddress:e.fromAddress,toAddress:e.toAddress,paymentToken:{token:{tokenAddress:i.originToken.address,chainId:i.originToken.chainId,decimals:i.originToken.decimals,symbol:i.originToken.symbol,name:i.originToken.name,priceUSDCents:i.originToken.priceUsd*100},amountWei:r.originAmount.toString(),amount:X(r.originAmount,i.originToken.decimals).toString(),amountUSDCents:Number(X(r.originAmount,i.originToken.decimals))*i.originToken.priceUsd*100},processingFee:{token:{tokenAddress:i.originToken.address,chainId:i.originToken.chainId,decimals:i.originToken.decimals,symbol:i.originToken.symbol,name:i.originToken.name,priceUSDCents:i.originToken.priceUsd*100},amountWei:e.feePayer==="sender"?(r.originAmount-r.destinationAmount).toString():"0",amount:e.feePayer==="sender"?X(r.originAmount-r.destinationAmount,i.originToken.decimals).toString():"0",amountUSDCents:e.feePayer==="sender"?Number(X(r.originAmount-r.destinationAmount,i.originToken.decimals))*i.originToken.priceUsd*100:0},estimatedGasCostUSDCents:0,client:e.client}}catch(n){throw console.error("Error getting buy with crypto transfer",n),n}}function wo(e){var j;const{title:n,onBack:s,receiverAddress:o,client:r,payer:i,onDone:d,chain:a,token:c,tokenAmount:l,transactionMode:f,setTransactionHash:x,payOptions:y}=e,[h,m]=S.useState("transfer"),[g,T]=S.useState({id:"idle"}),C=ee({queryKey:["transfer",W(c)?q:c.address,l,o,i.account.address,y==null?void 0:y.purchaseData],queryFn:async()=>await ko({client:r,fromAddress:i.account.address,toAddress:o,chainId:a.id,tokenAddress:W(c)?q:c.address,amount:l,purchaseData:y==null?void 0:y.purchaseData,feePayer:(y==null?void 0:y.mode)==="direct_payment"?y.paymentInfo.feePayer:void 0}),refetchInterval:30*1e3}),A=S.useMemo(()=>{if(h==="approve"&&g.id==="error"&&g.error)return g.error.toLowerCase().includes("user rejected")||g.error.toLowerCase().includes("user closed modal")||g.error.toLowerCase().includes("user denied")?{title:"Failed to Approve",message:"Your wallet rejected the approval request."}:{title:"Failed to Approve",message:"Your wallet failed to approve the transaction for an unknown reason. Please try again or contact support."};if((h==="transfer"||h==="execute")&&g.id==="error"&&g.error)return g.error.toLowerCase().includes("user rejected")?{title:"Failed to Confirm",message:"Your wallet rejected the confirmation request."}:{title:"Failed to Confirm",message:"Your wallet failed to confirm the transaction for an unknown reason. Please try again or contact support."}},[h,g]);if(C.isLoading)return t.jsxs(u,{p:"lg",children:[t.jsx(ne,{title:n,onBack:s}),t.jsxs(u,{flex:"column",center:"both",style:{minHeight:"300px"},children:[t.jsx(w,{y:"xl"}),t.jsx(ae,{size:"xl",color:"secondaryText"}),t.jsx(w,{y:"xl"})]})]});const b=((j=C.data)==null?void 0:j.paymentToken.amount)||l;return t.jsxs(u,{p:"lg",children:[t.jsx(ne,{title:n,onBack:s}),t.jsx(w,{y:"xl"}),f?t.jsxs(t.Fragment,{children:[t.jsx(fn,{steps:2,currentStep:h==="transfer"?1:2}),t.jsx(w,{y:"sm"}),t.jsx(k,{size:"sm",children:h==="transfer"?"Step 1 of 2 - Transfer funds":"Step 2 of 2 - Finalize transaction"}),t.jsx(w,{y:"md"})]}):t.jsxs(t.Fragment,{children:[t.jsx(k,{size:"sm",children:"Confirm payment"}),t.jsx(w,{y:"md"})]}),t.jsx(yn,{sender:i.account.address,receiver:o,client:r,fromToken:c,fromChain:a,toToken:c,toChain:a,fromAmount:f?l:b,toAmount:l}),t.jsx(w,{y:"lg"}),f&&t.jsxs(t.Fragment,{children:[t.jsx(w,{y:"sm"}),t.jsxs(u,{gap:"sm",flex:"row",style:{justifyContent:"space-between"},center:"y",color:"accentText",children:[t.jsx(Me,{isDone:h==="execute",isActive:h==="transfer",label:h==="transfer"?"Transfer":"Done"}),t.jsx(gn,{}),t.jsx(Me,{isDone:!1,label:"Finalize",isActive:h==="execute"})]}),t.jsx(w,{y:"lg"})]}),A&&t.jsxs(t.Fragment,{children:[t.jsx(xn,{title:A.title,message:A.message}),t.jsx(w,{y:"md"})]}),!f&&h==="execute"&&g.id==="done"&&t.jsxs(t.Fragment,{children:[t.jsxs(u,{flex:"row",gap:"xs",center:"both",color:"success",children:[t.jsx(Vt,{width:E.sm,height:E.sm}),t.jsx(k,{color:"success",size:"sm",children:"Payment completed"})]}),t.jsx(w,{y:"md"})]}),i.chain.id!==a.id?t.jsx(Ke,{fullWidth:!0,variant:"accent",switchChain:async()=>{await e.payer.wallet.switchChain(a)}}):t.jsxs(R,{variant:"accent",fullWidth:!0,disabled:g.id==="pending",onClick:async()=>{var D,v;if(h==="execute"){d();return}try{if(T({id:"pending"}),f){const B=W(c)?Gn({client:r,chain:a,to:o,value:pe(l)}):Kn({contract:Q({address:c.address,chain:a,client:r}),to:o,amount:l}),[I,F]=await Promise.all([Bt({account:e.payer.account,transaction:B}),Gt({contract:Q({address:W(c)?q:c.address,chain:a,client:r})})]);N({client:e.client,walletAddress:i.account.address,walletType:i.wallet.id,toChainId:a.id,toToken:W(c)?void 0:c.address,event:"transfer_confirmation_success_transaction_mode"}),(D=e.onSuccess)==null||D.call(e,To({token:c,chain:a,tokenMetadata:F,tokenAmount:f?l:b,fromAddress:i.account.address,toAddress:o,transaction:I})),m("execute"),T({id:"idle"})}else{const B=C.data;if(!B)throw new Error("Transfer data not found");if(B.approvalData&&await it({contract:Q({client:r,address:B.approvalData.tokenAddress,chain:L(B.approvalData.chainId)}),spender:B.approvalData.spenderAddress,owner:i.account.address})<BigInt(B.approvalData.amountWei)){m("approve"),N({client:e.client,walletAddress:i.account.address,walletType:i.wallet.id,toChainId:a.id,toToken:W(c)?void 0:c.address,event:"prompt_transfer_approval"});const _=Ee({contract:Q({client:r,address:B.approvalData.tokenAddress,chain:L(B.approvalData.chainId)}),spender:B.approvalData.spenderAddress,amountWei:BigInt(B.approvalData.amountWei)});await Bt({account:e.payer.account,transaction:_}),N({client:e.client,walletAddress:i.account.address,walletType:i.wallet.id,toChainId:a.id,toToken:W(c)?void 0:c.address,event:"transfer_approval_success"})}N({client:e.client,walletAddress:i.account.address,walletType:i.wallet.id,toChainId:a.id,toToken:W(c)?void 0:c.address,event:"prompt_transfer_confirmation"}),m("transfer");const I=B.transactionRequest,F=await ye({account:e.payer.account,transaction:I});x(F.transactionHash),T({id:"idle"}),N({client:e.client,walletAddress:i.account.address,walletType:i.wallet.id,toChainId:a.id,toToken:W(c)?void 0:c.address,event:"transfer_confirmation_success"})}}catch(B){console.error(B),T({id:"error",error:"error"in B?(v=B.error)==null?void 0:v.message:B==null?void 0:B.message})}},gap:"xs",children:[h==="execute"&&(g.id==="done"?"Done":"Continue"),h==="transfer"&&(g.id==="pending"?"Confirming":"Confirm"),h==="approve"&&(g.id==="pending"?"Approving":"Approve"),g.id==="pending"&&t.jsx(ae,{size:"sm",color:"accentButtonText"})]})]})}function To(e){const{token:n,chain:s,tokenMetadata:o,tokenAmount:r,fromAddress:i,toAddress:d,transaction:a}=e;return{status:"COMPLETED",subStatus:"SUCCESS",swapType:"TRANSFER",quote:{createdAt:new Date().toISOString(),fromToken:{chainId:s.id,tokenAddress:W(n)?q:n.address,decimals:o.decimals,symbol:o.symbol,name:o.name,priceUSDCents:0},toToken:{chainId:s.id,tokenAddress:W(n)?q:n.address,decimals:o.decimals,symbol:o.symbol,name:o.name,priceUSDCents:0},fromAmountWei:pe(r).toString(),fromAmount:r,toAmountWei:pe(r).toString(),toAmount:r,toAmountMin:r,toAmountMinWei:pe(r).toString(),estimated:{feesUSDCents:0,gasCostUSDCents:0,slippageBPS:0,toAmountMinUSDCents:0,toAmountUSDCents:0,fromAmountUSDCents:0,durationSeconds:0}},fromAddress:i,toAddress:d,source:{transactionHash:a.transactionHash,amount:r,amountWei:pe(r).toString(),amountUSDCents:0,completedAt:new Date().toISOString(),token:{chainId:s.id,tokenAddress:W(n)?q:n.address,decimals:o.decimals,symbol:o.symbol,name:o.name,priceUSDCents:0}}}}function jo(e){const[n,s]=S.useState();return n?t.jsx(kn,{title:e.title,onBack:e.onBack,onTryAgain:e.onTryAgain,swapTxHash:n,fromChain:e.chain,client:e.client,onDone:e.onDone,transactionMode:!1,isEmbed:e.isEmbed,quote:void 0,onSuccess:e.onSuccess}):t.jsx(wo,{...e,setTransactionHash:s})}function bo(){const e=Ue(),n=Te(),s=Ut(),[o,r]=S.useState();return S.useEffect(()=>{const a=o==null?void 0:o.wallet;function c(){if(!a){r(void 0);return}const l=a.getAccount(),f=a.getChain();r(l&&f?{account:l,chain:f,wallet:a}:void 0)}if(a){const l=a.subscribe("chainChanged",c),f=a.subscribe("accountChanged",c);return()=>{l(),f()}}},[o]),{payer:o||(n&&s&&e?{account:n,chain:s,wallet:e}:void 0),setPayer:r}}function Lo(e){e.payOptions.buyWithCrypto&&e.payOptions.buyWithCrypto.testMode;const n=tn(e.client);if(n.isError)return t.jsx(u,{style:{minHeight:"350px"},fullHeight:!0,flex:"row",center:"both",children:t.jsx(Nt,{title:"Something went wrong",onTryAgain:n.refetch})});if(!n.data)return t.jsx(ve,{});const s=e.supportedTokens?Object.entries(e.supportedTokens).map(([o,r])=>({chain:L(Number.parseInt(o)),tokens:r.map(i=>({...i,buyWithCryptoEnabled:!0,buyWithFiatEnabled:!0}))})):n.data;return t.jsx(Co,{...e,supportedDestinations:s})}function Co(e){var re,ce,te,je,be,Ce,Ae,Se,Ie,de,rt,ct,dt,lt,ut,ht,mt,ft,xt,yt,gt,kt,wt,Tt;const{client:n,supportedDestinations:s,connectLocale:o,payOptions:r}=e,i=Te(),{payer:d,setPayer:a}=bo(),[c,l]=S.useState({id:"main"}),{tokenAmount:f,setTokenAmount:x,toChain:y,setToChain:h,deferredTokenAmount:m,toToken:g,setToToken:T}=Xs({payOptions:r,supportedDestinations:s}),[C,A]=S.useState(!1),b=S.useCallback(()=>{l({id:"main"}),e.onDone()},[e.onDone]),j=ws({client:e.client,destinationChainId:y.id,destinationTokenAddress:W(g)?q:g.address}),D=S.useMemo(()=>Ft(s,r,e.supportedTokens),[e.supportedTokens,s,r]),v=S.useMemo(()=>{if(!j.data)return;const P=j.data;return Ft(P,r,e.supportedTokens)},[e.supportedTokens,j.data,r]);wn({client:e.client,sourceSupportedTokens:v||[],toChain:y,toToken:g,mode:r.mode});const{fromChain:B,setFromChain:I,fromToken:F,setFromToken:p}=eo({payOptions:r,supportedSources:j.data||[]}),{selectedCurrency:_,setSelectedCurrency:Z}=to({payOptions:r}),G=Zs({payOptions:e.payOptions,supportedDestinations:e.supportedDestinations,toChain:y,toToken:g}),M=G.buyWithCryptoEnabled===!1&&G.buyWithFiatEnabled===!1,K=ze(),ue=S.useCallback(P=>{var U,O;(O=(U=e.payOptions).onPurchaseSuccess)==null||O.call(U,{type:"crypto",status:P}),ke(K)},[e.payOptions.onPurchaseSuccess,K]),he=S.useCallback(P=>{var U,O;(O=(U=e.payOptions).onPurchaseSuccess)==null||O.call(U,{type:"fiat",status:P}),ke(K)},[e.payOptions.onPurchaseSuccess,K]);if(c.id==="connect-payer-wallet")return t.jsx(Qn,{accountAbstraction:(re=e.connectOptions)==null?void 0:re.accountAbstraction,appMetadata:(ce=e.connectOptions)==null?void 0:ce.appMetadata,chain:y||((te=e.connectOptions)==null?void 0:te.chain),chains:[y,...((je=e.connectOptions)==null?void 0:je.chains)||[]],client:e.client,connectLocale:e.connectLocale,isEmbed:e.isEmbed,onBack:()=>l(c.backScreen),onSelect:P=>{const U=P.getAccount(),O=P.getChain();P&&U&&O&&a({account:U,chain:O,wallet:P})},hiddenWallets:e.hiddenWallets,recommendedWallets:(be=e.connectOptions)==null?void 0:be.recommendedWallets,showAllWallets:((Ce=e.connectOptions)==null?void 0:Ce.showAllWallets)===void 0?!0:(Ae=e.connectOptions)==null?void 0:Ae.showAllWallets,walletConnect:(Se=e.connectOptions)==null?void 0:Se.walletConnect,wallets:(de=(Ie=e.connectOptions)==null?void 0:Ie.wallets)==null?void 0:de.filter(P=>P.id!=="inApp")});if(c.id==="swap-flow"&&d)return t.jsx(co,{title:e.title,transactionMode:r.mode==="transaction",isEmbed:e.isEmbed,client:n,onBack:()=>{l({id:"buy-with-crypto"})},buyWithCryptoQuote:c.quote,payer:d,isFiatFlow:!1,onDone:b,onTryAgain:()=>{l({id:"buy-with-crypto"})},onSuccess:ue,approvalAmount:c.approvalAmount});if(c.id==="fiat-flow"&&d){const U=((ct=(rt=e.payOptions)==null?void 0:rt.paymentInfo)==null?void 0:ct.sellerAddress)||d.account.address;return t.jsx(Vs,{title:e.title,transactionMode:r.mode==="transaction",quote:c.quote,onBack:()=>{l({id:"buy-with-fiat"})},client:n,testMode:e.payOptions.buyWithFiat!==!1&&((dt=e.payOptions.buyWithFiat)==null?void 0:dt.testMode)===!0,theme:typeof e.theme=="string"?e.theme:e.theme.type,onDone:b,isEmbed:e.isEmbed,payer:d,receiverAddress:U,onSuccess:he})}if(c.id==="transfer-flow"&&d&&i){const P=()=>l({id:"buy-with-crypto"}),O=((ut=(lt=e.payOptions)==null?void 0:lt.paymentInfo)==null?void 0:ut.sellerAddress)||i.address;return t.jsx(jo,{title:e.title,onBack:P,payer:d,client:e.client,chain:y,token:g,tokenAmount:f,receiverAddress:O,transactionMode:e.payOptions.mode==="transaction",payOptions:r,isEmbed:e.isEmbed,onDone:b,onTryAgain:()=>{l({id:"buy-with-crypto"})},onSuccess:ue})}if(c.id==="select-currency"){const P=()=>l(c.backScreen);return t.jsx(Is,{onSelect:U=>{P(),Z(U)},onBack:P})}if(c.id==="select-to-token"){const P=s.map(se=>se.chain),U=()=>l(c.backScreen),O=(ht=r==null?void 0:r.prefillBuy)==null?void 0:ht.allowEdits;return(O==null?void 0:O.token)===!1?t.jsx(po,{chains:P,client:e.client,connectLocale:e.connectLocale,setChain:h,goBack:U}):t.jsx(Yn,{onBack:U,tokenList:((y!=null&&y.id?D[y.id]:void 0)||[]).filter(se=>se.address.toLowerCase()!==q.toLowerCase()),onTokenSelect:se=>{T(se),U()},chain:y,chainSelection:(O==null?void 0:O.chain)!==!1?{chains:P,select:se=>{h(se)}}:void 0,connectLocale:o,client:n,modalTitle:e.title})}return t.jsx(u,{animate:"fadein",children:t.jsxs("div",{children:[c.id==="main"&&t.jsx(So,{title:e.title,payerAccount:d==null?void 0:d.account,client:n,onSelectBuyToken:()=>l({id:"select-to-token",backScreen:c}),payOptions:r,setTokenAmount:x,setToChain:h,setToToken:T,setFromChain:I,setFromToken:p,toChain:y,toToken:g,tokenAmount:f,connectOptions:e.connectOptions,setScreen:l,supportedDestinations:s,onBack:e.onBack,theme:e.theme,hasEditedAmount:C,setHasEditedAmount:A,enabledPaymentMethods:G}),(c.id==="select-payment-method"||c.id==="buy-with-crypto"||c.id==="buy-with-fiat"||c.id==="select-from-token")&&d&&t.jsxs(Io,{disabled:"prefillBuy"in r&&((ft=(mt=r.prefillBuy)==null?void 0:mt.allowEdits)==null?void 0:ft.amount)===!1||r.mode!=="fund_wallet",title:e.title,selectedChain:y,selectedToken:g,tokenAmount:f,setTokenAmount:x,client:n,onBack:()=>{(c.id==="buy-with-crypto"||c.id==="buy-with-fiat")&&G.buyWithCryptoEnabled?l({id:"select-from-token",backScreen:{id:"main"}}):c.id==="select-from-token"?l(c.backScreen):l({id:"main"})},children:[c.id==="buy-with-crypto"&&i&&t.jsx(uo,{setScreen:l,tokenAmount:m,toChain:y,toToken:g,fromChain:B,fromToken:F,showFromTokenSelector:()=>{l({id:"select-from-token",backScreen:c})},payer:d,client:n,isEmbed:e.isEmbed,onDone:b,payOptions:r,connectLocale:o,connectOptions:e.connectOptions,setPayer:a,activeAccount:i,setTokenAmount:x,setHasEditedAmount:A,disableTokenSelection:M===!0||r.buyWithCrypto!==!1&&((gt=(yt=(xt=r.buyWithCrypto)==null?void 0:xt.prefillSource)==null?void 0:yt.allowEdits)==null?void 0:gt.chain)===!1&&((Tt=(wt=(kt=r.buyWithCrypto)==null?void 0:kt.prefillSource)==null?void 0:wt.allowEdits)==null?void 0:Tt.token)===!1}),c.id==="buy-with-fiat"&&t.jsx(qs,{setScreen:l,tokenAmount:m,toChain:y,toToken:g,selectedCurrency:_,client:n,isEmbed:e.isEmbed,onDone:b,payOptions:r,theme:e.theme,showCurrencySelector:()=>{l({id:"select-currency",backScreen:c})},payer:d,setTokenAmount:x,setHasEditedAmount:A}),c.id==="select-from-token"&&j.data&&v&&t.jsx(fo,{fiatSupported:e.payOptions.buyWithFiat!==!1,client:e.client,sourceTokens:v,sourceSupportedTokens:v,toChain:y,toToken:g,tokenAmount:f,mode:r.mode,hiddenWallets:e.hiddenWallets,onConnect:()=>{l({id:"connect-payer-wallet",backScreen:c})},onPayWithFiat:()=>{l({id:"buy-with-fiat"})},onSelectToken:(P,U,O)=>{const se=P.getAccount();se&&(a({account:se,chain:O,wallet:P}),p(U),I(O)),l({id:"buy-with-crypto"})}})]})]})})}function Ao(e){const n=()=>{const s=H(Number(e.tokenAmount),6).toString();let o=s.replace(".","").length;return s.includes(".")&&(o+=.3),`calc(${`${Math.max(1,o)}ch + 2px`})`};return t.jsx("div",{children:t.jsxs(u,{flex:"row",gap:"sm",center:"y",style:{justifyContent:"space-between"},children:[t.jsxs(u,{flex:"row",gap:"xxs",center:"y",children:[t.jsx(Ht,{variant:"outline",pattern:"^[0-9]*[.,]?[0-9]*$",inputMode:"decimal",tabIndex:-1,placeholder:"0",type:"text","data-placeholder":e.tokenAmount==="",value:e.tokenAmount||"0",disabled:e.disabled,onClick:s=>{e.tokenAmount===""&&s.currentTarget.setSelectionRange(s.currentTarget.value.length,s.currentTarget.value.length)},onChange:s=>{let o=s.target.value;if(o=o.replace(",","."),o.startsWith(".")&&(o=`0${o}`),o.length>10)return;const r=Number(o);Number.isNaN(r)||(o.startsWith("0")&&!o.startsWith("0.")?e.setTokenAmount(o.slice(1)):e.setTokenAmount(o))},style:{border:"none",fontSize:V.lg,boxShadow:"none",borderRadius:"0",padding:"0",paddingBlock:"2px",fontWeight:600,textAlign:"left",width:n()}}),t.jsxs(u,{flex:"row",gap:"xxs",center:"y",children:[t.jsx(oe,{token:e.selectedToken,chain:e.selectedChain,size:"md",color:"secondaryText"}),t.jsx(Ge,{chain:e.selectedChain,client:e.client,size:"sm",token:e.selectedToken})]}),t.jsx(at,{chain:e.selectedChain,client:e.client,tokenAmount:e.tokenAmount,token:e.selectedToken,size:"sm"})]}),t.jsx(es,{chain:e.selectedChain,client:e.client,size:"sm",short:!0})]})})}function So(e){var C,A,b,j,D,v,B,I,F,p,_,Z;const{setTokenAmount:n,setToChain:s,setToToken:o,setFromChain:r,setFromToken:i,payerAccount:d,client:a,tokenAmount:c,payOptions:l,toToken:f,toChain:x,supportedDestinations:y,enabledPaymentMethods:h}=e,{buyWithCryptoEnabled:m,buyWithFiatEnabled:g}=h,T=!c;switch(l.mode){case"transaction":return t.jsx(Ts,{supportedDestinations:y,payUiOptions:l,payerAccount:d,connectOptions:e.connectOptions,client:a,onContinue:(G,M,K)=>{n(G),s(M),r(M),i(K),o(K),g&&!m?e.setScreen({id:"buy-with-fiat"}):e.setScreen({id:"select-from-token",backScreen:{id:"main"}})}});case"direct_payment":return t.jsx(gs,{client:a,payUiOptions:l,payerAccount:d,connectOptions:e.connectOptions,supportedDestinations:y,onContinue:(G,M,K)=>{n(G),s(M),r(M),i(K),o(K),g&&!m?e.setScreen({id:"buy-with-fiat"}):e.setScreen({id:"select-from-token",backScreen:{id:"main"}})}});default:return t.jsxs(u,{px:"lg",children:[t.jsx(w,{y:"lg"}),t.jsx(ne,{title:e.title,onBack:e.onBack}),t.jsx(w,{y:"xl"}),t.jsx(io,{value:c,onChange:async G=>{e.setHasEditedAmount(!0),n(G)},freezeAmount:((A=(C=l.prefillBuy)==null?void 0:C.allowEdits)==null?void 0:A.amount)===!1,freezeChainAndToken:((j=(b=l.prefillBuy)==null?void 0:b.allowEdits)==null?void 0:j.chain)===!1&&((v=(D=l.prefillBuy)==null?void 0:D.allowEdits)==null?void 0:v.token)===!1||l.buyWithCrypto!==!1&&((F=(I=(B=l.buyWithCrypto)==null?void 0:B.prefillSource)==null?void 0:I.allowEdits)==null?void 0:F.token)===!1&&((Z=(_=(p=l.buyWithCrypto)==null?void 0:p.prefillSource)==null?void 0:_.allowEdits)==null?void 0:Z.chain)===!1,token:f,chain:x,onSelectToken:e.onSelectBuyToken,client:e.client}),t.jsx(w,{y:"md"}),t.jsx(u,{flex:"column",gap:"sm",children:d?t.jsx(R,{variant:"accent",fullWidth:!0,disabled:T,"data-disabled":T,onClick:()=>{g&&!m?e.setScreen({id:"buy-with-fiat"}):e.setScreen({id:"select-from-token",backScreen:{id:"main"}}),N({event:"choose_payment_method_fund_wallet_mode",client:a,walletAddress:d.address,toChainId:x.id,toToken:W(f)?void 0:f.address})},children:"Continue"}):t.jsx("div",{children:t.jsx(Le,{...e.connectOptions,client:e.client,theme:e.theme,connectButton:{style:{width:"100%"}}})})}),t.jsx(w,{y:"lg"}),l.showThirdwebBranding!==!1&&t.jsxs(t.Fragment,{children:[t.jsx(nt,{link:"https://playground.thirdweb.com/connect/pay?utm_source=ub_text"}),t.jsx(w,{y:"sm"})]})]})}}function Io(e){return t.jsxs(u,{children:[t.jsx(u,{p:"lg",children:t.jsx(ne,{title:e.title,onBack:e.onBack})}),t.jsxs(u,{px:"lg",style:{paddingBottom:z.lg},children:[t.jsx(w,{y:"xs"}),t.jsx(Ao,{selectedToken:e.selectedToken,selectedChain:e.selectedChain,tokenAmount:e.tokenAmount,setTokenAmount:e.setTokenAmount,client:e.client,disabled:e.disabled}),t.jsx(w,{y:"sm"}),t.jsx(we,{}),t.jsx(w,{y:"sm"}),e.children]})]})}function Ft(e,n,s){if(s)return s;const o={},r=n.buyWithFiat===!1,i=n.buyWithCrypto===!1;for(const d of e)o[d.chain.id]=d.tokens.filter(a=>a.address===Jn?!1:a.buyWithCryptoEnabled===void 0&&a.buyWithFiatEnabled===void 0||a.buyWithCryptoEnabled&&a.buyWithFiatEnabled?!0:!(!a.buyWithCryptoEnabled&&r||!a.buyWithFiatEnabled&&i));return o}function po(e){return t.jsx(Zn,{client:e.client,connectLocale:e.connectLocale,showTabs:!1,onBack:e.goBack,chains:e.chains,closeModal:e.goBack,networkSelector:{renderChain(n){return t.jsx(Xn,{chain:n.chain,confirming:!1,switchingFailed:!1,onClick:()=>{e.setChain(n.chain),e.goBack()},client:e.client,connectLocale:e.connectLocale})}}})}export{Lo as default};
